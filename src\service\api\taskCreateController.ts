// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** 此处后端没有提供注释 POST /task_create */
export async function taskCreate(
  body: API.TaskCreateParam,
  options?: { [key: string]: any }
) {
  return request<API.ResultTaskCreateResult>('/task_create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /task_create_sync */
export async function taskCreateSync(
  body: API.TaskCreateSyncParam,
  options?: { [key: string]: any }
) {
  return request<API.ResultTaskCreateSyncResult>('/task_create_sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 GET /task_info */
export async function task(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultTaskInfo>('/task_info', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /task_infos */
export async function taskInfos(
  body: API.TaskInfoParam,
  options?: { [key: string]: any }
) {
  return request<API.ResultListTaskInfo>('/task_infos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}
