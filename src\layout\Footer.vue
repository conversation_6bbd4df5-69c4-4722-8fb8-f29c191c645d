<template>
  <footer class="bg-[#202020] text-white">
    <div class="w-[1400px] m-auto flex items-center justify-between py-14">
      <MyImage
        class="w-[144px] h-12"
        src="/images/logo-white.png"
        alt="wujieai"
      />
      <div class="h-[112px] flex gap-[341px]">
        <div class="flex flex-col gap-4">
          <div class="font-semibold text-[18px]">联系我们</div>
          <div class="text-white/70">
            <MyIcon icon="call" class="mr-2" :size="16" />86 571 2828 1822
          </div>
          <div class="text-white/70">
            <MyIcon icon="email" class="mr-2" :size="16" />
            <EMAIL>
          </div>
        </div>
        <div class="flex flex-col gap-4">
          <div class="font-semibold text-[18px]">关于我们</div>
          <div
            class="text-white/70 cursor-pointer"
            @click="router.push('/about')"
          >
            软件介绍
          </div>
          <div
            class="text-white/70 cursor-pointer"
            @click="router.push('/issue')"
          >
            常见问题
          </div>
          <div class="text-white/70 cursor-pointer" @click="getManual">
            用户手册
          </div>
        </div>
        <div class="flex flex-col gap-4">
          <div class="font-semibold text-[18px]">其他</div>
          <div class="text-white/70 cursor-pointer" @click="getUserAgreement">
            服务条款
          </div>
          <!-- <div class="text-white/70">授权协议</div> -->
        </div>
      </div>
    </div>
    <div
      class="flex justify-center gap-4 text-[14px] py-4 border-t border-[rgba(255,255,255,0.2)]"
    >
      <div>Copyright ©2025 Taology AI 保留所有权利</div>
      <div>浙ICP备2024073048号-3</div>
      <div>浙公网安备 4403694009259</div>
      <div>网信算备44032556725770601240021号</div>
    </div>
  </footer>
</template>
  <script setup lang="ts">
import { useRouter } from 'vue-router'
const router = useRouter()
const getManual = () => {
  window.open('https://www.yuque.com/shuangxi-m9ar4/umk4zm?#')
}
const getUserAgreement = () => {
  window.open('https://www.yuque.com/shuangxi-m9ar4/pmk17h?#')
}
</script>
  <style scoped></style>
  