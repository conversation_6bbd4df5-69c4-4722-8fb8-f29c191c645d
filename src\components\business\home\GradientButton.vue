<template>
  <div
    class="gradient-btn w-max text-[24px] font-semibold border-1 border-gradient rounded-full px-[68px] py-4 cursor-pointer group/btn"
  >
    <span class="text-gradient group-hover/btn:text-white">
      <slot />
    </span>
  </div>
</template>

<script lang="ts" setup></script>

<style scoped>
.gradient-btn:hover {
  background: linear-gradient(93.68deg, #ff87db 4.29%, #31c1ff 94.11%);
  box-shadow:
    0px 10px 26.7px 0px rgba(152, 121, 232, 0.38),
    0px 4px 10.2px 0px rgba(152, 121, 232, 0.25);

  &::before {
    display: none;
  }
}
</style>
