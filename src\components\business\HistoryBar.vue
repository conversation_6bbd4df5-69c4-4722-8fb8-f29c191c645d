<template>
  <div
    class="w-[72px] border-l bg-gradient-to-b from-[#ffe5fb] to-[#ffffff] h-full flex-shrink-0 flex flex-col"
    v-if="newData.length > 0"
  >
    <div class="flex flex-col items-center py-6 gap-4 flex-1 overflow-auto">
      <div
        v-for="image in newData"
        :key="image.id"
        class="w-10 h-10 rounded-lg flex-shrink-0 bg-neutral-50 cursor-pointer overflow-hidden"
        @click="debounceSelectPic(image)"
      >
        <MyImage
          v-if="image.status === 'SUCCESS' && !!image.result?.url"
          :src="image.result.thumbUrl ?? image.result?.url"
          class="w-full h-full flex items-center justify-center"
          tag="v400"
        />
        <div
          class="flex items-center justify-center w-full h-full px-2"
          v-else-if="image.status === 'GENERATING'"
        >
          <el-progress
            :percentage="(image?.completePercent ?? 0) * 100"
            :stroke-width="4"
            :show-text="false"
            color="var(--primary)"
            class="w-full"
          />
        </div>
        <div v-else class="flex items-center justify-center w-full h-full">
          <MyIcon icon="Warning" :size="20" class="text-neutral-400" />
        </div>
      </div>
    </div>
    <div
      class="h-[72px] bg-white flex items-center justify-center border-t border-primary"
    >
      <div
        class="size-10 rounded-lg bg-tertiary flex items-center justify-center cursor-pointer"
        @click="visible = true"
      >
        <MyIcon icon="Frame" class="text-tertiary" />
      </div>
    </div>
  </div>

  <el-drawer v-model="visible" direction="rtl" :with-header="false" :size="488">
    <History @close="visible = false" :drawType="drawType!" />
  </el-drawer>
</template>

<script setup lang="ts">
import { useAuth } from '@/hooks/useAuth'
import { useClickPreviewArtwork } from '@/hooks/useClickPreviewArtwork'
import { useHistoryList } from '@/hooks/useHistoryList'
import { useUpdateHistoryList } from '@/hooks/useUpdateHistoryList'
import { subMenuMap } from '@/layout/const'
import { useArtwork } from '@/store'
import { debounce, merge } from 'lodash'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const visible = ref(false)
const { isLogin } = useAuth()

const route = useRoute()
const artwork = useArtwork()

const drawType = computed(() => {
  const mainPathname = `/${route.path.split('/')[1] || ''}`
  const paths = subMenuMap[mainPathname] || []
  return paths?.filter(it => it.pathname === route.path)?.[0]?.drawType
})

const { data, fetchData } = useHistoryList()

const { handleSelectPic } = useClickPreviewArtwork()

const debounceSelectPic = debounce(
  (image: API.TaskUnitVO) => handleSelectPic(image),
  500
)

// 轮询，更新进度
const progressCache = useUpdateHistoryList({
  list: data,
})

const newData = computed(() => {
  return data?.value?.map(item => {
    if (progressCache.value[item.id!]) {
      return merge({}, item, progressCache.value[item.id!])
    }
    return item
  })
})
onMounted(() => {
  if (!isLogin) return
  fetchData({ drawType: drawType.value!, page: 0 })
  data.value.map(item => {
    console.log(item, '545')
  })
  console.log(data, '5885')
})

watch(
  () => artwork.isUpdate,
  value => {
    if (!value) return
    fetchData({ drawType: drawType.value!, page: 0 })
    artwork.setUpdateHistory(false)
  }
)
</script>

<style></style>
