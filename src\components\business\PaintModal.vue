<template>
  <MyDialog
    title="编辑重绘区域"
    :visible="visible"
    width="90%"
    :show-footer="false"
    @update:visible="emits('update:visible', $event)"
    top="0"
    style="margin: 0 auto"
    @opened="
      () => {
        dialogOpened = true
      }
    "
    @close="
      () => {
        dialogOpened = false
      }
    "
    :destroy-on-close="true"
  >
    <div
      class="w-full h-full flex flex-col"
      style="height: calc(100vh - 128px)"
    >
      <Paint
        :dialogOpened="dialogOpened"
        :bottomImage="bottomImage"
        :maskImage="maskImage"
        @cancel="emits('update:visible', false)"
        @confirm="emits('confirm', $event)"
      />
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const { visible, bottomImage, maskImage } = defineProps<{
  visible: boolean
  bottomImage?: string | null
  maskImage?: string
}>()
const dialogOpened = ref(false)
const emits = defineEmits(['update:visible', 'confirm'])
</script>
