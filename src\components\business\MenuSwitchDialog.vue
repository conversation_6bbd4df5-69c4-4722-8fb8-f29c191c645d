<template>
  <MyDialog
    title="设计特征（多选）"
    :visible="visible"
    width="840px"
    @update:visible="emits('update:visible', $event)"
    :show-footer="false"
  >
    <div style="display: flex; flex-wrap: wrap; margin-top: -10px">
      <div
        v-for="tag in tagsArray"
        :key="tag"
        style="margin-right: 10px; margin-top: 10px"
      >
        <el-tag closable type="info" @close="handleClose(tag)">
          {{ tag }}
        </el-tag>
      </div>
    </div>

    <div class="meau-preview">
      <ul style="display: flex; margin-bottom: 10px">
        <li
          :class="activeIndex == index ? 'isActive' : ''"
          v-for="(menu, index) in menus"
          :key="index"
          @click="handleSelect(index)"
        >
          {{ menu }}
        </li>
      </ul>
      <div style="display: flex">
        <div style="width: 110px; border-right: 1px solid #b4b4b4">
          <div
            class="asideItem"
            v-for="(item, index) in asideSelected.items"
            :key="index"
          >
            <div
              :class="activeIndex1 == index ? 'isActive1' : ''"
              @click="handleSelect1(index)"
              style="
                display: flex;
                justify-content: center;
                align-items: center;
                height: 40px;
                width: 96px;
                height: 26px;
                cursor: pointer;
              "
            >
              {{ item }}
            </div>
          </div>
        </div>
        <!-- 右侧 -->
        <div style="height: 520px">
          <div
            style="
              display: flex;
              flex-wrap: wrap;
              align-content: flex-start;
              padding-left: 10px;
              margin-top: -10px;
              height: 460px;
              overflow: auto;
            "
          >
            <div
              v-for="(item, index) in itemsSelected.items"
              :key="index"
              class="rightItems"
              @click="handleSelectClick(index)"
              :class="item.clickStates ? 'clicked-bg' : ''"
            >
              {{ item.label }}
            </div>
          </div>
          <div
            style="
              width: 700px;
              height: 60px;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-top: 10px;
            "
          >
            <div style="width: 400px">
              <MyButton type="primary" class="w-full" @click="finishSelect"
                >完成<span v-if="tagsArray.length > 0"
                  >({{ tagsArray.length }})</span
                ></MyButton
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </MyDialog>
</template>
  
  <script setup lang="ts">
// import { menus } from '@/layout/const'
import { onMounted, reactive, ref, computed } from 'vue'

const props = defineProps<{
  visible: boolean
}>()
let tagsArray1 = computed(() => {
  return props.item
})
const menus = [
  '款式',
  '风格',
  '面料',
  '工艺',
  '特殊工艺',
  '廓形',
  '情绪',
  '场景',
  '构图',
]
const asideArray = [
  {
    label: '服装款式',
    items: ['上装', '下装', '裙装', '套装'],
  },
  {
    label: '风格',
    items: ['自然', '复古', '都市', '个性', '设计', '休闲'],
  },
  {
    label: '面料',
    items: ['材质', '质感', '图案'],
  },
  {
    label: '工艺',
    items: ['缝制工艺', '剪裁结构', '辅助材料', '结构设计'],
  },
  {
    label: '特殊工艺',
    items: ['印花工艺', '水洗处理', '绣花', '司马克', '装饰元素'],
  },
  {
    label: '廓形',
    items: ['字母廓形', '整体轮廓', '合身度'],
  },
  {
    label: '情绪',
    items: ['松弛', '个性', '其他'],
  },
  {
    label: '场景',
    items: ['环境', '视觉风格', '功能用途', '主题'],
  },
  {
    label: '构图',
    items: ['构图', '范围', '焦点'],
  },
]
const itemsArray = ref([
  [
    {
      label: '上装',
      items: [
        { label: 'T恤', clickStates: false },
        { label: '衬衫', clickStates: false },
        { label: '背心', clickStates: false },
        { label: '卫衣', clickStates: false },
        { label: '针织衫', clickStates: false },
        { label: '条纹T恤', clickStates: false },
        { label: '印花T恤', clickStates: false },
        { label: '无袖', clickStates: false },
        { label: '吊带', clickStates: false },
        { label: '外套', clickStates: false },
        { label: '夹克', clickStates: false },
        { label: '皮夹克', clickStates: false },
        { label: '牛仔外套', clickStates: false },
        { label: '西装', clickStates: false },
        { label: '风衣', clickStates: false },
        { label: '马甲', clickStates: false },
        { label: '长袖', clickStates: false },
        { label: '短袖', clickStates: false },
      ],
    },
    {
      label: '下装',
      items: [
        { label: '牛仔裤', clickStates: false },
        { label: '阔腿裤', clickStates: false },
        { label: '直筒裤', clickStates: false },
        { label: '西装裤', clickStates: false },
        { label: '哈伦裤', clickStates: false },
        { label: '喇叭裤', clickStates: false },
        { label: '背带裤', clickStates: false },
        { label: '长裤', clickStates: false },
        { label: '半裙', clickStates: false },
        { label: '短裤', clickStates: false },
        { label: '中裤', clickStates: false },
      ],
    },
    {
      label: '裙装',
      items: [
        { label: '连衣裙', clickStates: false },
        { label: '吊带裙', clickStates: false },
        { label: '衬衫裙', clickStates: false },
        { label: '长裙', clickStates: false },
      ],
    },
    {
      label: '套装',
      items: [
        { label: '连体裤', clickStates: false },
        { label: '套装', clickStates: false },
      ],
    },
  ],
  [
    {
      label: '自然',
      items: [
        { label: '自然', clickStates: false },
        { label: '田园', clickStates: false },
        { label: '度假', clickStates: false },
        { label: '慵懒', clickStates: false },
        { label: '波西米亚', clickStates: false },
        { label: '清新', clickStates: false },
        { label: '可爱', clickStates: false },
      ],
    },
    {
      label: '复古',
      items: [
        { label: '复古', clickStates: false },
        { label: '文艺', clickStates: false },
        { label: '艺术', clickStates: false },
        { label: '民族风', clickStates: false },
        { label: '知性', clickStates: false },
        { label: '经典', clickStates: false },
      ],
    },
    {
      label: '都市',
      items: [
        { label: '都市', clickStates: false },
        { label: '现代', clickStates: false },
        { label: '极简', clickStates: false },
        { label: '精致', clickStates: false },
        { label: '学院', clickStates: false },
        { label: '优雅', clickStates: false },
      ],
    },
    {
      label: '个性',
      items: [
        { label: '个性', clickStates: false },
        { label: '街头', clickStates: false },
        { label: '前卫', clickStates: false },
        { label: '狂野', clickStates: false },
        { label: '野性', clickStates: false },
        { label: '性感', clickStates: false },
      ],
    },
    {
      label: '设计',
      items: [
        { label: '拼接', clickStates: false },
        { label: '解构', clickStates: false },
        { label: '拼贴', clickStates: false },
        { label: '混搭', clickStates: false },
        { label: '印花', clickStates: false },
      ],
    },
    {
      label: '休闲',
      items: [
        { label: '休闲', clickStates: false },
        { label: '简约', clickStates: false },
        { label: '中性', clickStates: false },
        { label: '日常', clickStates: false },
        { label: '运动', clickStates: false },
        { label: '节日', clickStates: false },
      ],
    },
  ],
  [
    {
      label: '材质',
      items: [
        { label: '亚麻', clickStates: false },
        { label: '丝绸', clickStates: false },
        { label: '皮革', clickStates: false },
        { label: '麂皮', clickStates: false },
        { label: '丹宁', clickStates: false },
        { label: '毛呢', clickStates: false },
        { label: '棉麻', clickStates: false },
        { label: '雪纺', clickStates: false },
        { label: '帆布', clickStates: false },
        { label: '蕾丝', clickStates: false },
        { label: '纱', clickStates: false },
        { label: '丝绒', clickStates: false },
        { label: '尼龙', clickStates: false },
        { label: '混纺', clickStates: false },
        { label: '牛仔', clickStates: false },
        { label: '醋酸', clickStates: false },
        { label: '绣花', clickStates: false },
        { label: '提花', clickStates: false },
        { label: '人丝人棉', clickStates: false },
      ],
    },
    {
      label: '质感',
      items: [
        { label: '柔软', clickStates: false },
        { label: '垂坠', clickStates: false },
        { label: '硬挺', clickStates: false },
        { label: '挺括', clickStates: false },
        { label: '丝绸感', clickStates: false },
        { label: '肌理感', clickStates: false },
        { label: '轻薄', clickStates: false },
        { label: '透视', clickStates: false },
        { label: '褶皱', clickStates: false },
        { label: '天然', clickStates: false },
        { label: '光泽', clickStates: false },
        { label: '褶皱', clickStates: false },
      ],
    },
    {
      label: '图案',
      items: [
        { label: '印花', clickStates: false },
        { label: '条纹', clickStates: false },
        { label: '格纹', clickStates: false },
        { label: '镂空', clickStates: false },
      ],
    },
  ],
  [
    {
      label: '缝制工艺',
      items: [
        { label: '包边', clickStates: false },
        { label: '明线', clickStates: false },
        { label: '缝线', clickStates: false },
        { label: '锁边', clickStates: false },
        { label: '平缝', clickStates: false },
        { label: '车缝', clickStates: false },
        { label: '绗缝', clickStates: false },
        { label: '平整', clickStates: false },
      ],
    },
    {
      label: '裁剪结构',
      items: [
        { label: '拼接', clickStates: false },
        { label: '贴袋', clickStates: false },
      ],
    },
    {
      label: '辅助材料',
      items: [
        { label: '纽扣', clickStates: false },
        { label: '拉链', clickStates: false },
      ],
    },
    {
      label: '结构设计',
      items: [
        { label: '开衩', clickStates: false },
        { label: '不对称', clickStates: false },
        { label: '不规则', clickStates: false },
        { label: '透视', clickStates: false },
        { label: '压褶', clickStates: false },
        { label: '抽褶', clickStates: false },
        { label: '系带', clickStates: false },
        { label: '褶皱', clickStates: false },
        { label: 'V领', clickStates: false },
        { label: '衬衫领', clickStates: false },
        { label: '船领', clickStates: false },
        { label: '垂褶领', clickStates: false },
        { label: '吊带领', clickStates: false },
        { label: '高垂领', clickStates: false },
        { label: '立领', clickStates: false },
        { label: '抹胸领', clickStates: false },
        { label: '飘带领', clickStates: false },
        { label: '斜领', clickStates: false },
        { label: '一字领', clickStates: false },
        { label: '圆领', clickStates: false },
      ],
    },
  ],
  [
    {
      label: '印花工艺',
      items: [
        { label: '数码印花', clickStates: false },
        { label: '艺术印花', clickStates: false },
        { label: '印花图案', clickStates: false },
        { label: '豹纹印花', clickStates: false },
        { label: '动物纹', clickStates: false },
        { label: '花朵', clickStates: false },
        { label: '条纹', clickStates: false },
        { label: '格子', clickStates: false },
      ],
    },
    {
      label: '水洗处理',
      items: [
        { label: '做旧', clickStates: false },
        { label: '水洗', clickStates: false },
      ],
    },
    {
      label: '绣花',
      items: [
        { label: '镂空绣', clickStates: false },
        { label: '绳绣', clickStates: false },
        { label: '平绣', clickStates: false },
      ],
    },
    {
      label: '司马克',
      items: [
        { label: '花式司马克', clickStates: false },
        { label: '常规司马克', clickStates: false },
      ],
    },
    {
      label: '装饰元素',
      items: [
        { label: '亮片', clickStates: false },
        { label: '流苏', clickStates: false },
        { label: '毛边', clickStates: false },
        { label: '拼接', clickStates: false },
        { label: '绳结', clickStates: false },
        { label: '撞色', clickStates: false },
        { label: '蕾丝', clickStates: false },
        { label: '刺绣logo', clickStates: false },
        { label: '编织', clickStates: false },
        { label: '钉珠', clickStates: false },
        { label: '烫钻', clickStates: false },
        { label: '线迹', clickStates: false },
        { label: '手工滴针', clickStates: false },
        { label: '胶印', clickStates: false },
      ],
    },
  ],
  [
    {
      label: '字母廓形',
      items: [
        { label: 'H型', clickStates: false },
        { label: 'O型', clickStates: false },
        { label: 'A型', clickStates: false },
        { label: 'X型', clickStates: false },
        { label: 'I型', clickStates: false },
      ],
    },
    {
      label: '整体轮廓',
      items: [
        { label: '上紧下松', clickStates: false },
        { label: '上宽下窄', clickStates: false },
        { label: '上窄下宽', clickStates: false },
        { label: '上松下直', clickStates: false },
        { label: '上松下紧', clickStates: false },
        { label: '上松下宽', clickStates: false },
        { label: '倒三角', clickStates: false },
        { label: '直筒', clickStates: false },
        { label: '直筒型', clickStates: false },
        { label: '锥形', clickStates: false },
        { label: '阔腿', clickStates: false },
        { label: '垂坠', clickStates: false },
        { label: '飘逸', clickStates: false },
      ],
    },
    {
      label: '合身度',
      items: [
        { label: '修身', clickStates: false },
        { label: '合身', clickStates: false },
        { label: '紧身', clickStates: false },
        { label: '贴身', clickStates: false },
        { label: '宽松', clickStates: false },
      ],
    },
  ],
  [
    {
      label: '松弛',
      items: [
        { label: '惬意', clickStates: false },
        { label: '自然', clickStates: false },
        { label: '舒适', clickStates: false },
        { label: '清新', clickStates: false },
        { label: '自在', clickStates: false },
        { label: '慵懒', clickStates: false },
        { label: '随意', clickStates: false },
        { label: '随性', clickStates: false },
      ],
    },
    {
      label: '个性',
      items: [
        { label: '自信', clickStates: false },
        { label: '独立', clickStates: false },
        { label: '率性', clickStates: false },
        { label: '不羁', clickStates: false },
        { label: '酷', clickStates: false },
        { label: '神秘', clickStates: false },
        { label: '疏离', clickStates: false },
      ],
    },
    {
      label: '其他',
      items: [
        { label: '文艺', clickStates: false },
        { label: '创意', clickStates: false },
        { label: '知性', clickStates: false },
        { label: '优雅', clickStates: false },
        { label: '简约', clickStates: false },
        { label: '时尚', clickStates: false },
        { label: '朴素', clickStates: false },
        { label: '性感', clickStates: false },
        { label: '俏皮', clickStates: false },
        { label: '浪漫', clickStates: false },
        { label: '有趣', clickStates: false },
        { label: '活力', clickStates: false },
        { label: '活泼', clickStates: false },
      ],
    },
  ],
  [
    {
      label: '环境',
      items: [
        { label: '影棚', clickStates: false },
        { label: '室内', clickStates: false },
        { label: '街头', clickStates: false },
        { label: '户外', clickStates: false },
        { label: '海边', clickStates: false },
        { label: '工作室', clickStates: false },
        { label: '居家', clickStates: false },
        { label: '派对', clickStates: false },
      ],
    },
    {
      label: '视觉风格',
      items: [
        { label: '极简', clickStates: false },
        { label: '纯色', clickStates: false },
        { label: '干净', clickStates: false },
        { label: '现代', clickStates: false },
        { label: '时尚', clickStates: false },
        { label: '艺术', clickStates: false },
      ],
    },
    {
      label: '功能用途',
      items: [
        { label: '展示', clickStates: false },
        { label: '宣传', clickStates: false },
        { label: '电商', clickStates: false },
        { label: '专业', clickStates: false },
        { label: '突出', clickStates: false },
      ],
    },
    {
      label: '功能用途',
      items: [
        { label: '通勤', clickStates: false },
        { label: '休闲', clickStates: false },
        { label: '度假', clickStates: false },
        { label: '约会', clickStates: false },
        { label: '正式', clickStates: false },
        { label: '运动', clickStates: false },
      ],
    },
  ],
  [
    {
      label: '构图',
      items: [
        { label: '居中', clickStates: false },
        { label: '垂直居中', clickStates: false },
        { label: '中心', clickStates: false },
        { label: '对称', clickStates: false },
        { label: '平铺', clickStates: false },
        { label: '规整', clickStates: false },
        { label: '留白', clickStates: false },
      ],
    },
    {
      label: '范围',
      items: [
        { label: '全身', clickStates: false },
        { label: '半身', clickStates: false },
        { label: '特写', clickStates: false },
        { label: '正面', clickStates: false },
        { label: '侧面', clickStates: false },
        { label: '背面', clickStates: false },
        { label: '整体', clickStates: false },
      ],
    },
    {
      label: '焦点',
      items: [
        { label: '突出服装', clickStates: false },
        { label: '突出整体', clickStates: false },
        { label: '突出主体', clickStates: false },
        { label: '突出廓形', clickStates: false },
        { label: '聚焦', clickStates: false },
        { label: '细节', clickStates: false },
      ],
    },
  ],
])
const asideSelected = ref()
const itemsSelected = ref()
const emits = defineEmits(['update:visible', 'getItems'])
const activeIndex = ref(0)
const activeIndex1 = ref(0)
const tagsArray = ref([])

const handleClose = (tag: string) => {
  tagsArray.value.splice(tagsArray.value.indexOf(tag), 1)
  itemsArray.value.forEach((item, index2) => {
    item.forEach((it, index) => {
      it.items.forEach((i, index1) => {
        if (i.label == tag) {
          itemsArray.value[index2][index].items[index1].clickStates = false
        }
      })
    })
  })
}
const handleSelect1 = (key: number) => {
  console.log(key)
  activeIndex1.value = key
  itemsSelected.value = itemsArray.value[activeIndex.value][key]
}
const handleSelect = (key: number) => {
  console.log(key)
  activeIndex.value = key
  asideSelected.value = asideArray[key]
  console.log(asideSelected.value, '655')
  activeIndex1.value = 0
  itemsSelected.value = itemsArray.value[key][0]
}
const textArray = ref([])

const handleSelectClick = (index: number) => {
  console.log(menus[activeIndex.value], '658')
  let flag =
    itemsArray.value[activeIndex.value][activeIndex1.value].items[index]
      .clickStates
  console.log(activeIndex.value, activeIndex1.value, '886')
  itemsArray.value[activeIndex.value][activeIndex1.value].items[
    index
  ].clickStates =
    !itemsArray.value[activeIndex.value][activeIndex1.value].items[index]
      .clickStates
  flag = !flag
  console.log(flag, '588')
  console.log(
    itemsArray.value[activeIndex.value][activeIndex1.value].items[index],
    '365'
  )
  if (flag) {
    tagsArray.value.push(
      itemsArray.value[activeIndex.value][activeIndex1.value].items[index].label
    )
    textArray.value.push({
      label1: menus[activeIndex.value],
      label2:
        itemsArray.value[activeIndex.value][activeIndex1.value].items[index]
          .label,
    })
  } else {
    tagsArray.value = tagsArray.value.filter(
      item =>
        item !=
        itemsArray.value[activeIndex.value][activeIndex1.value].items[index]
          .label
    )
  }
  console.log(tagsArray.value, '365')

  console.log(textArray.value, '388')
}
const finishSelect = () => {
  emits('update:visible', false)
  textArray.value.forEach(item => {
    console.log(item, '564')
    if (tagsArray.value.indexOf(item.label2) == -1) {
      textArray.value.splice(textArray.value.indexOf(item), 1)
    }
  })
  emits('getItems', tagsArray.value, textArray.value)
}
onMounted(() => {
  asideSelected.value = asideArray[0]
  itemsSelected.value = itemsArray.value[0][0]
  itemsArray.value.forEach(item => {
    item.forEach(it => {
      it.items.forEach(i => {
        i.clickStates = false
      })
    })
  })
  itemsArray.value[0][0].items.forEach(item => {
    item.clickStates = false
  })
  console.log(itemsArray.value, '565')
})
</script>
  
  <style scoped lang="scss">
.meau-preview {
  color: rgba(119, 119, 119, 1);
  font-family: PingFang SC;
  li {
    width: 80px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .asideItem {
    width: 100px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .isActive {
    color: rgba(245, 164, 233, 1);
  }
  .isActive1 {
    background: rgba(254, 232, 255, 0.96);
    color: rgba(51, 51, 51, 1);
    border-radius: 4px;
    font-weight: 600;
  }
  .clicked-bg {
    border: 1px solid #f5a4e9 !important;
    background: rgba(245, 164, 233, 0.34);
    color: rgba(23, 23, 23, 1);
  }
  .rightItems {
    width: 150px;
    height: 75px;
    border: 1px solid #b4b4b4;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: 10px;
    margin-top: 10px;
  }
}
</style>
  