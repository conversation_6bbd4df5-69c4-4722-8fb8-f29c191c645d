<template>
  <SubPageLayout :confirm-loading="isLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          @callBack="callBackFun"
          @getWh="getSize"
        />
        <FormItem label="延伸幅度">
          <MySlider
            :max="1"
            :min="0"
            :step="0.01"
            v-model="sliderStep"
            show-input
          />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea class="h-full" v-if="loading == ''" />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer1
          :load="loading"
          :previewImages="imgAll"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
          :wh="imgSize1"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
  <script setup lang="ts">
import { patternExtraction } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork } from '@/store'
import { onBeforeUnmount, ref, watch, computed } from 'vue'
import axios from 'axios'
const uploadImage = ref<string | null | undefined>()
const confirmLoading = ref(false)
const activeIndex = ref(0)
const artwork = useArtwork()
const loading = ref('')
const sliderStep = ref(0.5)
const originImg = ref()
const count = ref(2)
const imgAll = ref([])
const imgSize1 = ref()
const imgSize = ref(false)
const imgID = ref()
const {
  pollCreatingArtworks,
  stopCreatingPoll,
  loading: pollingLoading,
} = useCreatingPoll()

// 合并API调用loading和轮询loading状态
const isLoading = computed(() => confirmLoading.value || pollingLoading.value)

const callBackFun = e => {
  originImg.value = e
}
const selectChange = e => {
  count.value = e
}
const getSize = e => {
  imgSize.value = e
}
const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  imgSize1.value = imgSize.value
  imgAll.value = []
  activeIndex.value = 0
  confirmLoading.value = true
  loading.value = 'GENERATING'
  let formData = new FormData()
  formData.append('image_load', originImg.value)
  formData.append('denoise', Number(sliderStep.value))
  formData.append('n_iter', Number(count.value))
  axios({
    url: 'http://172.19.3.206:8020/comfyui/api/image_tend',
    method: 'post',
    data: formData,
  })
    .then(res => {
      if (res.data.request_id) {
        imgID.value = res.data.request_id
        console.log(imgID.value, '455')
        checking()
      }
    })
    .catch(() => {
      // loading.value = false
      ElMessage.error('接口出错')
      loading.value = 'fail'
      confirmLoading.value = false
    })
  // confirmLoading.value = false
  // pollCreatingArtworks({
  //   ids: res?.data?.ids ?? [],
  // })
}
//递归获取图片
function checking() {
  setTimeout(() => {
    axios({
      url: `http://172.19.3.206:8020/comfyui/api/check_result/${imgID.value}`,
      method: 'get',
    }).then(result => {
      console.log(result)
      if (result.data.error_code == 0) {
        // loading.value = false
        if (result.data.data.images.length == 0) {
          loading.value = 'fail'
        } else {
          for (let i = 0; i < result.data.data.images.length; i++) {
            imgAll.value.push(result.data.data.images[i])
          }
          loading.value = 'SUCCESS'
        }
        confirmLoading.value = false
        console.log(imgAll.value)
      } else if (result.data.error_code == 1) {
        // loading.value = false
        confirmLoading.value = false
        loading.value = 'fail'
        ElMessage.error(result.data.msg)
      } else {
        checking()
      }
    })
  }, 3000)
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.flowerPatternExtractionFields?.inImgUrl
  }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
  <style scoped lang="scss"></style>
  