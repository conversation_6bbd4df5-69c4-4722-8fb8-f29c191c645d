// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** 此处后端没有提供注释 GET /inner_model_codes */
export async function modelCodes(options?: { [key: string]: any }) {
  return request<number[]>('/inner_model_codes', {
    method: 'GET',
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /inner_queue_infos */
export async function queueInfo(
  body: number,
  options?: { [key: string]: any }
) {
  return request<API.QueueInfo>('/inner_queue_infos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /inner_server_num */
export async function serverNum(
  body: number,
  options?: { [key: string]: any }
) {
  return request<number>('/inner_server_num', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /inner_task_create */
export async function taskCreate1(
  body: API.TaskCreateParam,
  options?: { [key: string]: any }
) {
  return request<API.TaskCreateResultInfo[]>('/inner_task_create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /inner_task_create_sync */
export async function taskCreateSync1(
  body: API.TaskCreateSyncParam,
  options?: { [key: string]: any }
) {
  return request<API.TaskCreateSyncResult>('/inner_task_create_sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /inner_tasks_create */
export async function tasksCreate(
  body: API.TasksCreateParam,
  options?: { [key: string]: any }
) {
  return request<API.TaskCreateResultInfo[]>('/inner_tasks_create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /task_info_by_base_id */
export async function taskInfoByBaseId(
  body: number,
  options?: { [key: string]: any }
) {
  return request<API.TaskInfo>('/task_info_by_base_id', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}
