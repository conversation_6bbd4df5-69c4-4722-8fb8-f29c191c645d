<template>
  <div class="my-step-input" :class="class">
    <el-input-number
      v-model="modelValue"
      :min="min"
      :max="max"
      size="large"
      :controls="false"
      v-bind="$attrs"
      :disabled="disabled"
    />
    <div
      class="icon"
      :class="{ disabled: disabledMin }"
      @click="handleDecrease"
      v-if="!onlyInput"
    >
      <el-icon class="text-base"><Minus /></el-icon>
    </div>
    <div
      class="icon"
      :class="{ disabled: disabledMax }"
      @click="handleIncrease"
      v-if="!onlyInput"
    >
      <el-icon class="text-base"><Plus /></el-icon>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
interface IStepInputProps {
  min?: number
  max?: number
  disabled?: boolean
  onlyInput?: boolean
  step?: number
  class?: string
}
const {
  min = 0,
  max = 10,
  disabled = false,
  step = 1,
  onlyInput = false,
} = defineProps<IStepInputProps>()

const modelValue = defineModel<number>({ default: null })

const disabledMin = computed(() => {
  return (
    (modelValue.value !== 0 && !modelValue.value) ||
    modelValue.value <= min ||
    disabled
  )
})
const disabledMax = computed(() => {
  return (
    (modelValue.value !== 0 && !modelValue.value) ||
    modelValue.value >= max ||
    disabled
  )
})

// 减少
const handleDecrease = () => {
  if (
    (modelValue.value !== 0 && !modelValue.value) ||
    modelValue.value <= min ||
    disabled
  )
    return
  modelValue.value -= step
}
// 增加
const handleIncrease = () => {
  if (
    (modelValue.value !== 0 && !modelValue.value) ||
    modelValue.value >= max ||
    disabled
  )
    return
  modelValue.value += step
}
</script>
<style scoped lang="scss">
.my-step-input {
  @apply flex space-x-[10px] items-center h-[52px];
  :deep(.el-input-number) {
    @apply w-full h-full;
  }
  :deep(.el-input) {
    @apply w-full h-full;
    &.is-disabled {
      .el-input__wrapper {
        @apply border-tertiary bg-transparent;
        &:hover {
          @apply border-tertiary;
        }
      }
    }
    .el-input__wrapper {
      @apply border rounded-xl border-tertiary shadow-none;

      &:hover {
        @apply border-brand;
      }
      .el-input__inner {
        @apply font-semibold text-primary text-sm;
      }
    }
  }

  .icon {
    @apply flex items-center flex-shrink-0 justify-center w-[52px] h-[52px] border border-neutral-300 rounded-[12px] cursor-pointer select-none;

    &:hover {
      @apply border-brand;
    }
  }
  .disabled {
    @apply text-disabled cursor-not-allowed;

    &:hover {
      @apply border border-tertiary;
    }
  }
}
</style>
