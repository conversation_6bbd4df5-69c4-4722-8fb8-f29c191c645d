<script setup lang="ts">
import { computed, ref, reactive, onBeforeMount } from 'vue'
import MyDialog from '@/components/ui/MyDialog.vue'
import { updateUserProfile } from '@/clients/api/base'
import { useUserInfoStore } from '@/store'
import { useUpload } from '@/hooks/useUpload'

const { visible } = defineProps<{ visible: boolean }>()
const userStore = useUserInfoStore()

const form = reactive({
  avatar: '',
  nickname: '',
})

const onUrlChange = (url: string) => {
  form.avatar = url
}
const { beforeUpload, customUpload, progress, loading } = useUpload({
  // sizeLimit: sizeLimit,
  onUrlChange: onUrlChange,
  // fileType: fileType,
})

onBeforeMount(() => {
  form.avatar = userStore.base?.avatar
  form.nickname = userStore.base?.nickname
})

const onSubmit = () => {
  if (!form.nickname) {
    ElMessage.error('请输入昵称')
    return
  }
  updateUserProfile({
    nickname: form.nickname,
    avatar: form.avatar,
  }).then(resp => {
    if (resp.data) {
      ElMessage.success('编辑成功')
      userStore.getUserInfo()
      userStore.setUpdateProfileVisible(false)
    }
  })
}
</script>
<template>
  <MyDialog
    title="个人资料"
    needFillBackground
    :visible="visible"
    :width="480"
    :showFooter="false"
    class="profileModal"
    @close="userStore.setUpdateProfileVisible(false)"
  >
    <el-form
      ref="formRef"
      label-position="top"
      :model="form"
      class="login-form"
    >
      <el-form-item label="头像" prop="avatar">
        <el-upload
          class="avatar-uploader"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :auto-upload="true"
          :http-request="customUpload"
          accept="image/*"
        >
          <div class="avatar-wrap">
            <el-avatar :size="88" :src="form.avatar" />
            <div class="change-mask">更换头像</div>
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <my-input
          placeholder="请输入昵称"
          v-model="form.nickname"
          :maxlength="10"
        />
      </el-form-item>
    </el-form>
    <div>
      <my-button type="primary" class="w-full" @click="onSubmit"
        >确定</my-button
      >
    </div>
  </MyDialog>
</template>
<style scoped lang="scss">
.profileModal {
  :deep(.el-dialog__footer) {
    padding-top: 0;
  }
  :deep(.dialog-footer .tao-btn) {
    width: 100%;
  }
}
.avatar-wrap {
  position: relative;

  .change-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    color: #fff;
  }
  &:hover {
    .change-mask {
      display: flex;
    }
  }
  :deep(.el-avatar) {
    display: block;
  }
}
</style>
