export function saveAs(href: string | Blob, fileName: string) {
  const isBlob = href instanceof Blob
  const aLink = document.createElement('a')
  aLink.href = isBlob ? window.URL.createObjectURL(href) : href
  aLink.download = fileName
  aLink.click()
  if (isBlob) setTimeout(() => URL.revokeObjectURL(aLink.href), 100)
}

// 支持cdn文件下载
export function xhrDownload(
  url: string,
  fileName?: string,
  fileType: string = 'image'
) {
  return new Promise((reslove, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.responseType = 'blob'

    xhr.onload = () => {
      if (xhr.status === 200) {
        const defaultName = url.substring(url.lastIndexOf('/') + 1)
        saveAs(xhr.response, decodeURIComponent(fileName || defaultName))
        reslove(fileName)
      }
    }
    xhr.onerror = err => reject(err)

    xhr.open('get', url, true)

    if (fileType === 'image') {
      // 为了排除webp
      xhr.setRequestHeader('accept', 'image/jpeg, image/png')
      // 不使用缓存
      xhr.setRequestHeader('If-Modified-Since', '0')
    }

    xhr.send()
  })
}
