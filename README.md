## [swagger 2.x](https://v2.kubb.dev/) 文档生成

- [kubb.config.ts](/kubb.config.ts) 入口配置文件
- [axios instance](/src/axiosClient.ts) axios 实例

```bash
###### clients 目录自动生成 ######
# 正式
yarn gentypes
# 测试
yarn gentypes:test
```

- [api](/src/clients/apis) 生成的 api 接口定义
- [types](/src/clients/types) 生成的 api 接口类型定义

```ts
<script setup lang="ts">
// 类型定义
import { findPetsByStatusQueryParamsStatus, Pet } from '@/clients'
// 接口定义
import { findPetsByStatus } from '@/clients/apis'
import { onMounted, ref } from 'vue'

const pets = ref<Pet[]>([])
onMounted(() => {
  findPetsByStatus({
    status: findPetsByStatusQueryParamsStatus.available,
  }).then(res => {
    pets.value = res as Pet[]
  })
})
</script>
```

## elementplus 按需加载 sass 的一些警告

锁定 sass-embedded 版本为 1.79.x，可以解决警告问题，详细见[https://github.com/element-plus/element-plus/issues/18648](https://github.com/element-plus/element-plus/issues/18648)

## [volta](https://docs.volta.sh/guide/#how-does-it-work) 分项目管理node版本

和n，nvm全局管理工具不冲突，可以项目采用volta管理node，全局采用其他

```bash
# install Volta
curl https://get.volta.sh | bash
source ~/.bash_profile
```

```bash
volta pin node@20
volta pin yarn@1.22.21
```

```js
//packag.json会产生volta标识
"volta": {
  "node": "20.18.0",
  "yarn": "1.22.21"
}
```
