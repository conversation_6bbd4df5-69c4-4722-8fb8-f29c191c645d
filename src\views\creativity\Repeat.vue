<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <FormItem label="模式">
          <div class="grid grid-cols-2 gap-2">
            <div
              class="mode"
              :class="{ selected: mode === 'FLOWER_PATTERN' }"
              @click="mode = 'FLOWER_PATTERN'"
            >
              花型
            </div>
            <div
              class="mode"
              :class="{ selected: mode === 'FABRIC' }"
              @click="mode = 'FABRIC'"
            >
              面料
            </div>
          </div>
        </FormItem>
        <FormItem label="循环规格（长×宽）">
          <div class="flex items-center justify-between">
            <StepInputNumber
              v-model="rows"
              :min="1"
              :max="20"
              :onlyInput="true"
              class="!w-[144px] !h-10"
            />
            <MyIcon icon="close" :size="14" class="text-tertiary mx-2" />
            <StepInputNumber
              v-model="cols"
              :min="1"
              :max="20"
              :onlyInput="true"
              class="!w-[144px] !h-10"
            />
          </div>
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { patternCycle } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, ref, watch } from 'vue'
import StepInputNumber from '@/components/ui/StepInput.vue'
import MyIcon from '@/components/ui/MyIcon.vue'

const uploadImage = ref<string | null | undefined>()
const mode = ref<'FLOWER_PATTERN' | 'FABRIC'>('FLOWER_PATTERN')
const rows = ref(2)
const cols = ref(2)
const confirmLoading = ref(false)
const activeIndex = ref(0)

const artwork = useArtwork()
const userStore = useUserInfoStore()

const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  try {
    confirmLoading.value = true
    const res = await patternCycle({
      num: 1,
      drawParam: {
        initImage: uploadImage.value,
        mode: mode.value,
        rows: rows.value,
        cols: cols.value,
      },
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    ElMessage.error(error.message || '出错了')
  }
}

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    !!params.id && artwork.setCreatingArtworks([params])
    uploadImage.value = params?.patternCycleFields?.initImage
    mode.value = params?.patternCycleFields?.mode ?? 'FLOWER_PATTERN'
    rows.value = params?.patternCycleFields?.rows ?? 2
    cols.value = params?.patternCycleFields?.cols ?? 2
  },
  { deep: true, immediate: true }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss">
.mode {
  @apply text-primary font-semibold text-sm rounded-xl h-12 cursor-pointer border border-secondary border-solid flex items-center justify-center;

  &.selected {
    @apply border-brand text-brand;
  }
}
</style>
