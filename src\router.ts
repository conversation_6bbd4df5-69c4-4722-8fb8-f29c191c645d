import { createWebHistory, createRouter } from 'vue-router'

/** 花型模块 */
const flowerChildren = [
  {
    path: '/flower/layer',
    component: () => import('./views/flower/Layer1.vue'),
  },
  {
    path: '/flower/extract',
    component: () => import('./views/flower/Extract.vue'),
  },
]

export const router = createRouter({
  history: createWebHistory(),
  scrollBehavior: () => ({ left: 0, top: 0 }),

  routes: [
    {
      path: '/',
      component: () => import('./layout/NoneAsideLayout.vue'),
      children: [
        {
          path: '/',
          component: () => import('./views/Home.vue'),
        },
        {
          path: '/components',
          component: () => import('./views/Components.vue'),
        },
        {
          path: '/issue',
          component: () => import('./views/aboutUs/issue.vue'),
        },
        {
          path: '/about',
          component: () => import('./views/aboutUs/about.vue'),
        },
      ],
    },
    {
      path: '/',
      component: () => import('./layout/index.vue'),
      children: [
        {
          path: '/flower',
          redirect: flowerChildren[0].path,
        },
        ...flowerChildren,
        {
          path: '/craft',
          component: () => import('./views/craft/CraftReplace.vue'),
        },
        {
          path: '/craft/craftExtension',
          component: () => import('./views/craft/craftExtension.vue'),
        },
        {
          path: '/craft/fabricReplace',
          component: () => import('./views/craft/fabricReplace.vue'),
        },
        {
          path: '/flux',
          component: () => import('./views/flux/Text.vue'),
        },
        {
          path: '/flux/Img',
          component: () => import('./views/flux/Img.vue'),
        },
        //智能抠图
        {
          path: '/image',
          component: () => import('./views/image/Remove.vue'),
        },
        //分辨率提升
        {
          path: '/image/resup',
          component: () => import('./views/image/ResUp.vue'),
        },
        //局部重绘
        {
          path: '/image/redraw',
          name: 'redraw',
          component: () => import('./views/image/Redraw.vue'),
        },
        {
          path: '/extension',
          component: () => import('./views/image/Redraw.vue'),
        },
        {
          path: '/extension/style',
          component: () => import('./views/extension/style.vue'),
        },
        {
          path: '/model/line',
          component: () => import('./views/extension/line.vue'),
        },
        {
          path: '/model',
          component: () => import('./views/model/Change.vue'),
        },
        {
          path: '/model/swap',
          component: () => import('./views/virtual/fit.vue'),
        },
        {
          path: '/model/clothes',
          component: () => import('./views/model/clothes.vue'),
        },
        {
          path: '/styleOrder',
          component: () => import('./views/style/select.vue'),
        },
        /** 各种协议 */
        {
          path: '/agreement',
          component: () => import('./views/Agreement.vue'),
        },
      ],
    },
    {
      path: '/:page',
      component: () => import('./views/NotFound.vue'),
    },
  ],
})
