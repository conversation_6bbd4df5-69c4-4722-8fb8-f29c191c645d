<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          label="款式图片"
        />
        <PictureUpload
          v-model:value="uploadImage1"
          :drag="true"
          label="面料图片"
        />
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { createTaskMetac } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useImgwork } from '@/store'
const useImg = useImgwork()
const uploadImage = ref()
const uploadImage1 = ref()
const confirmLoading = ref(false)
const activeIndex = ref(0)
const artwork = useArtwork()
const userStore = useUserInfoStore()
const count = ref(2)
const selectChange = (e: number) => {
  count.value = e
}
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()
const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传款式图片')
    return
  }
  if (!uploadImage1.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传面料图片')
    return
  }
  try {
    confirmLoading.value = true
    const res = await createTaskMetac({
      material_url: uploadImage1.value,
      img_url: uploadImage.value,
      batch_size: count.value,
      mode: 'renewal',
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.change_head_plusFields?.imageUrl
    uploadImage1.value = params?.change_head_plusFields?.imageMaterialUrl
    count.value = params?.change_head_plusFields?.batchSize
  }
)
onMounted(async () => {
  uploadImage.value = useImg.imgUrlFabric
})
onBeforeUnmount(() => {
  stopCreatingPoll()
  useImg.setUpdateUrlFabric('')
})
</script>
    <style scoped lang="scss">
:deep(.el-button:hover) {
  color: #606022;
  background: none;
  border-color: #dcdfe6;
}
</style>
      