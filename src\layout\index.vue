<script setup lang="ts">
import Header from './Header.vue'
import { computed, onMounted, ref, unref } from 'vue'
import { useRouter, useRoute, onBeforeRouteUpdate } from 'vue-router'
import { subMenuMap } from './const'
import { useArtwork, useUserInfoStore } from '@/store'
import { ClickOutside as vClickOutside } from 'element-plus'
const userInfo = useUserInfoStore()
const artwork = useArtwork()
const showVideoDialog = ref(false)
const showTalkDialog = ref(false)
const router = useRouter()
const route = useRoute()
const subMenus = computed(() => {
  const mainPathname = `/${route.path.split('/')[1] || ''}`
  return (subMenuMap as any)[mainPathname] || []
})
const buttonRef = ref()
const popoverRef = ref()
const onClickOutside = () => {
  unref(popoverRef).popperRef?.delayHide?.()
}
onMounted(() => {
  userInfo.getUserInfo()
})
const videoUrl = ref('')
const jumpTalk = () => {
  showTalkDialog.value = !showTalkDialog.value
  showVideoDialog.value = false
}
const jumpUse = () => {
  console.log(route.path)
  showVideoDialog.value = true
  showTalkDialog.value = false
  switch (route.path) {
    case '/flux':
      videoUrl.value = '/video/text.mp4'
      break
    case '/flux/Img':
      videoUrl.value = '/video/img.mp4'
      break
    case '/craft':
      videoUrl.value = '/video/craft.mp4'
      break
    case '/craft/fabricReplace':
      videoUrl.value = '/video/fabric.mp4'
      break
    case '/flower/layer':
      videoUrl.value = '/video/layer.mp4'
      break
    case '/extension':
      videoUrl.value = '/video/redraw.mp4'
      break
    case '/styleOrder':
      videoUrl.value = '/video/style.mp4'
      break
    case '/model':
      videoUrl.value = '/video/change.mp4'
      break
    case '/model/swap':
      videoUrl.value = '/video/swap.mp4'
      break
    case '/model/line':
      videoUrl.value = '/video/line.mp4'
      break
    case '/model/clothes':
      videoUrl.value = '/video/clothes.mp4'
      break
    case '/image':
      videoUrl.value = '/video/remove.mp4'
      break
    case '/image/resup':
      videoUrl.value = '/video/resup.mp4'
      break
    case '/image/redraw':
      videoUrl.value = '/video/redraw.mp4'
      break
    case '/extension/style':
      videoUrl.value = '/video/extensionStyle.mp4'
      break
  }
}
const handleSelect = (key: string) => {
  if (key) {
    router.push(key)
  } else {
    ElMessage.error('研发中')
  }
  console.log(key, '658')
}

onBeforeRouteUpdate((to, from, next) => {
  // 重置参数
  artwork.setCreatingArtworks([])
  next()
})
</script>

<template>
  <div class="wrap">
    <Header />
    <el-container>
      <el-aside width="208px" class="pageAside">
        <el-menu
          active-text-color="#fff"
          background-color="transparent"
          text-color="var(--neutral-900)"
          class="mainMenu"
          @select="handleSelect"
          :default-active="route.path"
          :route="route"
          v-if="!!subMenus.length"
        >
          <el-menu-item
            v-for="menu in subMenus"
            :index="menu.pathname"
            :disabled="menu.pathname == ''"
          >
            <MyIcon :icon="menu.icon" :size="24" class="font-normal mr-2" />
            <span>{{ menu.label }}</span>
          </el-menu-item>
        </el-menu>
        <div @mouseleave="showVideoDialog = false">
          <div class="text-neutral-900 text-base font-bold pl-4 leading-[44px]">
            <div
              class="flex items-center mb-2 cursor-pointer"
              @click="router.push('/issue')"
            >
              <MyImage
                src="/images/wentifankui.png"
                style="margin-right: 3px; margin-left: -2px"
              />
              <span>常见问题</span>
            </div>
            <div
              class="flex items-center mb-2 cursor-pointer"
              @mouseenter="jumpUse"
            >
              <el-icon class="mr-2 font-light">
                <MyIcon icon="course" :size="24" />
              </el-icon>
              <span>使用教程</span>
            </div>
            <div
              class="cursor-pointer flex items-center"
              style="width: 100px"
              v-click-outside="onClickOutside"
              ref="buttonRef"
            >
              <MyImage
                src="/images/email.png"
                style="margin-right: 3px; margin-left: -2px"
              />
              <span>联系客服</span>
            </div>
            <el-popover
              ref="popoverRef"
              :virtual-ref="buttonRef"
              trigger="click"
              virtual-triggering
              :width="300"
              title="联系客服"
              placement="right-end"
              popper-class="custom-popper"
            >
              <div>电话号码：86-0571-28281822</div>
              <div style="margin-top: 10px">
                邮箱：<EMAIL>
              </div>
            </el-popover>
          </div>
          <div v-if="showVideoDialog" class="aisdeDialog">
            <video controls="controls" style="width: 100%; height: 100%">
              <source :src="videoUrl" type="video/mp4" />
            </video>
          </div>
        </div>
        <!-- <div
          v-if="showTalkDialog"
          class="aisdeDialog"
          style="background: #fbdef5"
        >
          <MyImage
            :src="'/images/talk.jpg'"
            style="width: 100%; height: 100%"
          ></MyImage>
        </div> -->
      </el-aside>
      <el-main class="mainContent">
        <router-view />
      </el-main>
    </el-container>
  </div>
  <LoginModal :visible="!!userInfo.loginType" v-if="!!userInfo.loginType" />
  <SettingLoginModal
    :visible="!!userInfo.settingLoginType"
    v-if="!!userInfo.settingLoginType"
  />
  <UpdateProfileModal
    :visible="userInfo.updateProfileVisible"
    v-if="userInfo.updateProfileVisible"
  />
  <ValidateAccountModal
    :visible="!!userInfo.validateAccountInfo"
    v-if="!!userInfo.validateAccountInfo"
  />
</template>

<style>
.custom-popper {
  /* background-color: #737373 !important; */
  /* color: #fff !important; */
  height: 130px;
  font-family: PingFang SC;
  :first-child {
    margin-bottom: 20px;
    margin-top: 10px;
    /* text-align: center; */
    font-weight: 500;
  }
}
</style>
<style scoped lang="scss">
.wrap {
  background: linear-gradient(294.9deg, #ffffff 80.46%, #ffe5fb 100%);
  min-width: 1440px;
  overflow: auto;
}

.pageAside {
  @apply p-6 flex flex-col justify-between;
  height: calc(100vh - 80px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.mainMenu {
  height: 100%;
  @apply border-0;
  .el-menu-item {
    @apply h-[44px] leading-[44px] mb-2 rounded-lg text-primary !py-4 text-base font-semibold;

    &:hover {
      @apply bg-white;
    }
  }
  .is-active {
    @apply bg-linear-primary;
    box-shadow: 0 0.3125rem 0.625rem -0.3125rem rgba(0, 0, 0, 0.3);
  }
}
.mainContent {
  height: calc(100vh - 80px);
  padding: 0;
}
.aisdeDialog {
  width: 300px;
  height: 200px;
  /* background: pink; */
  position: absolute;
  bottom: 30px;
  z-index: 99;
  left: 150px;
}
</style>
