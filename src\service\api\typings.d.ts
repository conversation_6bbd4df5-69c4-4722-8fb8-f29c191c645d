declare namespace API {
  type AccountTimesRewardPageRequest = {
    pageNum?: number
    pageSize?: number
    timeRangeBegin?: number
    timeRangeEnd?: number
    rewardUserPhone?: string
    operatorUserPhone?: string
  }

  type AccountTimesRewardRequest = {
    eventName?: string
    timesReward?: number
    rewardUid?: number
  }

  type ArtworkCreateResultVO = {
    /** Unique identifier of the artwork */
    ids?: number[]
  }

  type authRegisterParams = {
    phone: string
    verificationCode: string
  }

  type BehaviorVerifyDTO = {
    /** The authentication code */
    authCode?: string
    /** The token */
    token?: string
  }

  type CommonDrawReqFabricChangeRequest = {
    num?: number
    drawParam?: FabricChangeRequest
  }

  type CommonDrawReqFabricPatternCreationFieldsVO = {
    num?: number
    drawParam?: FabricPatternCreationFieldsVO
  }

  type CommonDrawReqLayerSeparationRequest = {
    num?: number
    drawParam?: LayerSeparationRequest
  }

  type CommonDrawReqPatternCycleFieldsVO = {
    num?: number
    drawParam?: PatternCycleFieldsVO
  }

  type CommonDrawReqPatternExtractionRequest = {
    num?: number
    drawParam?: PatternExtractionRequest
  }

  type CommonDrawReqPatternReplaceFieldsVO = {
    num?: number
    drawParam?: PatternReplaceFieldsVO
  }

  type CommonDrawReqStyleExtensionFieldsVO = {
    num?: number
    drawParam?: StyleExtensionFieldsVO
  }

  type CompleteCallBackParam = {
    statusCode: number
    taskId?: number
    taskKey?: string
    taskResult?: string
    extraInfo?: string
    returnInfo?: string
    innerServiceInfo?: InnerServiceInfo
    completeTime?: number
    taskCode?: number
    aiPicId?: number
    queueSize?: number
    imageUrl?: string
    controlImageUrl?: string
    runtime?: string
    exception?: PicFailMessage
    height?: number
    width?: number
    startGenTime?: number
    serverId?: number
  }

  type EmailResetRequest = {
    /** User's phone number */
    phone?: string
    /** Verification code sent to phone */
    verificationCode?: string
    /** New email to be set */
    email?: string
  }

  type FabricChangeRequest = {
    /** URL of the style image to be modified */
    styleImageUrl?: string
    /** URL of the new fabric image to be applied to the style */
    fabricImageUrl?: string
    /** Opacity of the fabric image */
    opacity?: number
    /** Whether to harmonize the fabric image with the style image */
    imageHarmonize?: boolean
    /** URL of the mask image to be applied to the fabric image */
    addMaskUrl?: string
    /** URL of the mask image to be removed from the fabric image */
    removeMaskUrl?: string
  }

  type FabricPatternCreationFieldsVO = {
    /** 底图 */
    initImage?: string
    /** 参考模式 */
    referenceModel?: 'STYLE' | 'CONTENT'
    /** 画面描述 */
    prompt?: string
  }

  type FabricReplaceFieldsVO = {
    styleImageUrl?: string
    fabricImageUrl?: string
    opacity?: number
    imageHarmonize?: boolean
    addMaskUrl?: string
    removeMaskUrl?: string
  }

  type FlowerPatternExtractionFieldsVO = {
    inImgUrl?: string
    prompt?: string
  }

  type GeneralInformationVO = {
    /** User agreement */
    userAgreement?: string
    /** Privacy policy */
    privacyPolicy?: string
  }

  type getDrawTaskUnitDetailByIdParams = {
    drawTaskUnitId: number
  }

  type InnerServiceInfo = {
    serviceType?: number
    serviceId?: number
  }

  type LayerSeparationFieldsVO = {
    inImgUrl?: string
    numColors?: number
  }

  type LayerSeparationRequest = {
    imageUrl?: string
    layerCount?: number
  }

  type LoginRequest = {
    /** Defines the login method */
    loginMethod?:
    | 'PHONE_VERIFICATION'
    | 'EMAIL_VERIFICATION'
    | 'PASSWORD'
    | 'PHONE_SECURITY_VERIFICATION'
    | 'EMAIL_SECURITY_VERIFICATION'
    /** Username (phone or email depending on login method) */
    username?: string
    /** User's password */
    password?: string
    /** Verification code (if using phone/email verification) */
    verificationCode?: string
  }

  type modelGenAvgTimeParams = {
    modelCode: number
  }

  type OssUploadTokenVO = {
    /** OSS access key */
    token?: string
    /** OSS access region */
    region?: string
  }

  type PageResponseRewardTimesRecordVO = {
    list?: RewardTimesRecordVO[]
    pageNum?: number
    pageSize?: number
    total?: number
  }

  type PageResponseTaskUnitDetailVO = {
    list?: TaskUnitDetailVO[]
    pageNum?: number
    pageSize?: number
    total?: number
  }

  type PageResponseTaskUnitVO = {
    list?: TaskUnitVO[]
    pageNum?: number
    pageSize?: number
    total?: number
  }

  type PageResponseUserResponseVO = {
    list?: UserResponseVO[]
    pageNum?: number
    pageSize?: number
    total?: number
  }

  type pageTimesAccountRewardParams = {
    request: AccountTimesRewardPageRequest
  }

  type PasswordResetRequest = {
    /** User's phone number */
    phone?: string
    /** Verification code sent to phone */
    verificationCode?: string
    /** New password to be set */
    newPassword?: string
  }

  type PatternCycleFieldsVO = {
    /** 底图 */
    initImage?: string
    /** 模式 */
    mode?: 'FLOWER_PATTERN' | 'FABRIC'
    /** 循环行数 */
    rows?: number
    /** 循环列数 */
    cols?: number
  }

  type PatternExtractionRequest = {
    prompt?: string
    imageUrl?: string
  }

  type PatternReplaceFieldsVO = {
    /** 底图 */
    initImage?: string
    /** 粘贴区域 */
    pasteArea?: 'PARTIAL' | 'OVERALL'
    /** 区域图 */
    pasteAreaImage?: string
    /** 元素图 */
    elementImage?: string
    /** 图案密度 */
    patternDensity?: number
  }

  type PicFailMessage = {
    code?: number
    msg?: string
  }

  type preLayerSeparationParams = {
    imageUrl: string
  }

  type PreLayerSeparationResultVO = {
    layerCount?: number
  }

  type prePatternExtractionParams = {
    imageUrl: string
  }

  type PrePatternExtractionResultVO = {
    tags?: string[]
  }

  type QueueInfo = {
    taskCode?: number
    queueNum?: number
    serverNum?: number
    generatingNum?: number
    expectedQueueTime?: number
    lastOneWaitedTime?: number
  }

  type ResultArtworkCreateResultVO = {
    code?: string
    message?: string
    data?: ArtworkCreateResultVO
    success?: boolean
  }

  type ResultBoolean = {
    code?: string
    message?: string
    data?: boolean
    success?: boolean
  }

  type ResultGeneralInformationVO = {
    code?: string
    message?: string
    data?: GeneralInformationVO
    success?: boolean
  }

  type ResultListTaskInfo = {
    code?: string
    message?: string
    data?: TaskInfo[]
    success?: boolean
  }

  type ResultListTaskUnitDetailVO = {
    code?: string
    message?: string
    data?: TaskUnitDetailVO[]
    success?: boolean
  }

  type ResultListTaskUnitVO = {
    code?: string
    message?: string
    data?: TaskUnitVO[]
    success?: boolean
  }

  type ResultListUserResponseVO = {
    code?: string
    message?: string
    data?: UserResponseVO[]
    success?: boolean
  }

  type ResultLong = {
    code?: string
    message?: string
    data?: number
    success?: boolean
  }

  type ResultMapStringBoolean = {
    code?: string
    message?: string
    data?: Record<string, any>
    success?: boolean
  }

  type ResultOssUploadTokenVO = {
    code?: string
    message?: string
    data?: OssUploadTokenVO
    success?: boolean
  }

  type ResultPageResponseRewardTimesRecordVO = {
    code?: string
    message?: string
    data?: PageResponseRewardTimesRecordVO
    success?: boolean
  }

  type ResultPageResponseTaskUnitDetailVO = {
    code?: string
    message?: string
    data?: PageResponseTaskUnitDetailVO
    success?: boolean
  }

  type ResultPageResponseTaskUnitVO = {
    code?: string
    message?: string
    data?: PageResponseTaskUnitVO
    success?: boolean
  }

  type ResultPageResponseUserResponseVO = {
    code?: string
    message?: string
    data?: PageResponseUserResponseVO
    success?: boolean
  }

  type ResultPreLayerSeparationResultVO = {
    code?: string
    message?: string
    data?: PreLayerSeparationResultVO
    success?: boolean
  }

  type ResultPrePatternExtractionResultVO = {
    code?: string
    message?: string
    data?: PrePatternExtractionResultVO
    success?: boolean
  }

  type ResultString = {
    code?: string
    message?: string
    data?: string
    success?: boolean
  }

  type ResultTaskCreateResult = {
    code?: string
    message?: string
    data?: TaskCreateResult
    success?: boolean
  }

  type ResultTaskCreateSyncResult = {
    code?: string
    message?: string
    data?: TaskCreateSyncResult
    success?: boolean
  }

  type ResultTaskInfo = {
    code?: string
    message?: string
    data?: TaskInfo
    success?: boolean
  }

  type ResultTaskUnitDetailVO = {
    code?: string
    message?: string
    data?: TaskUnitDetailVO
    success?: boolean
  }

  type ResultUserResponseVO = {
    code?: string
    message?: string
    data?: UserResponseVO
    success?: boolean
  }

  type RewardTimesRecordVO = {
    id?: number
    eventName?: string
    createTime?: number
    times?: number
    rewardUserNick?: string
    operatorName?: string
  }

  type ServerInfoParam = {
    mmsId: number
    modelCode?: number
    jobId?: number
    startStatus?: string
    serverName?: string
    token?: string
    serverHttpUrl: string
    sshInfo?: string
    snapshotId?: number
  }

  type SimpleDrawReq = {
    num?: number
    drawParam?: string
    workflowKey?: string
  }

  type SimpleTaskResult = {
    url?: string
    thumbUrl?: string
    type?: 'PNG' | 'PSD' | 'SVG' | 'HD_PSD'
  }

  type SmsSendTemplateReq = {
    mobile?: string
    areaCode?: string
    params?: string[]
    templateId?: string
    sourceIp?: string
  }

  type StyleExtensionFieldsVO = {
    /** 底图 */
    initImage?: string
    /** 画面描述 */
    prompt?: string
  }

  type TaskCreateParam = {
    taskCode: number
    param: string
    num: number
    accelerateStep?: number
    callbackUrl?: string
    requestAddress?: string
    taskTimeoutSeconds?: number
    expectedGenerateSeconds?: number
    limitExpectedSeconds?: number
    innerServiceInfo?: InnerServiceInfo
    retryTimes?: number
    oldProcess?: boolean
    uid?: number
  }

  type TaskCreateResult = {
    taskCode?: number
    createResult?: TaskCreateResultInfo[]
  }

  type TaskCreateResultInfo = {
    taskId?: number
    innerServiceInfo?: InnerServiceInfo
    expectedQueueSeconds?: number
    expectedGenerateSeconds?: number
  }

  type TaskCreateSyncParam = {
    taskCode: number
    param: string
    taskTimeoutSeconds?: number
    acquireResourceTimeoutSeconds?: number
    requestAddress?: string
  }

  type TaskCreateSyncResult = {
    taskCode?: number
    result?: string
  }

  type TaskInfo = {
    taskKey?: string
    innerServiceInfo?: InnerServiceInfo
    status?: number
    result?: string
    expectedSeconds?: number
    expectedQueueSeconds?: number
    expectedGenerateSeconds?: number
    startGenTime?: number
    taskType?: number
    completePercent?: number
  }

  type TaskInfoParam = {
    taskKeys?: string[]
  }

  type TaskModelInfoVo = {
    taskCode: number
    desc?: string
    taskModelType?: 'ASYNC' | 'SYNC'
    timeOutTime: number
  }

  type taskParams = {
    taskKey: string
  }

  type TaskResult = {
    url?: string
    thumbUrl?: string
    type?: 'PNG' | 'PSD' | 'SVG' | 'HD_PSD'
    extra?: SimpleTaskResult[]
  }

  type TasksCreateParam = {
    taskCode: number
    params: string[]
    num: number
    accelerateStep?: number
    callbackUrl?: string
    requestAddress?: string
    taskTimeoutSeconds?: number
    expectedGenerateSeconds?: number
    limitExpectedSeconds?: number
    serviceType?: number
    serviceIds?: number[]
    uid?: number
    retryTimes?: number
    oldProcess?: boolean
  }

  type TaskUnitDetailVO = {
    id?: number
    expectedSeconds?: number
    completePercent?: number
    status?: 'GENERATING' | 'SUCCESS' | 'FAIL'
    scheduleTime?: number
    completeTime?: number
    result?: TaskResult
    seed?: number
    drawType?: number
    patternCycleFields?: PatternCycleFieldsVO
    styleExtensionFields?: StyleExtensionFieldsVO
    fabricPatternCreationFields?: FabricPatternCreationFieldsVO
    patternReplaceFields?: PatternReplaceFieldsVO
    layerSeparationFields?: LayerSeparationFieldsVO
    flowerPatternExtractionFields?: FlowerPatternExtractionFieldsVO
    fabricReplaceFields?: FabricReplaceFieldsVO
  }

  type TaskUnitIdsReq = {
    drawTaskUnitIds?: number[]
  }

  type TaskUnitPageRequest = {
    drawType?: number
    startCreateTime?: number
    endCreateTime?: number
    page?: number
    pageSize?: number
  }

  type TaskUnitVO = {
    id?: number
    expectedSeconds?: number
    completePercent?: number
    status?: 'GENERATING' | 'SUCCESS' | 'FAIL'
    scheduleTime?: number
    completeTime?: number
    result?: TaskResult
    seed?: number
  }

  type UploadTokenRequest = {
    /** The file name to be uploaded */
    fileName?: string
    /** The original file name */
    originalFileName?: string
    /** The folder name to be uploaded */
    dirName?: string
  }

  type UserQueryPageRequest = {
    userQueryRequest?: UserQueryRequest
    page?: number
    size?: number
  }

  type UserQueryRequest = {
    /** User's nickname */
    nickname?: string
    /** User's phone number */
    phone?: string
    /** Whether the user has enabled security verification */
    securityEnabled?: boolean
    /** Defines the role type */
    roleType?: 'ADMIN' | 'USER'
  }

  type UserResponseVO = {
    /** User's nickname */
    nickname?: string
    /** User's avatar */
    avatar?: string
    /** User's phone number */
    phone?: string
    /** User's email address */
    email?: string
    /** Unique identifier of the user */
    uid?: number
    /** Remaining drawing times */
    drawingTimes?: number
    /** Whether the user has enabled security verification */
    securityEnabled?: boolean
    /** Defines the role type */
    roleType?: 'ADMIN' | 'USER'
    /** Registration time */
    registerTime?: number
    /** Whether the user has initialized their password */
    passwordInitialized?: boolean
  }

  type UserSettingsUpdateRequest = {
    /** User ID to update settings for */
    uid?: number
    /** Defines the role type */
    role?: 'ADMIN' | 'USER'
    /** Enable or update security authentication (optional) */
    securityEnabled?: boolean
  }

  type UserUpdateRequest = {
    /** User's nickname */
    nickname?: string
    /** User's avatar */
    avatar?: string
    /** User's phone number */
    phone?: string
    /** User's email address */
    email?: string
  }

  type VerificationCodeSendRequest = {
    /** The contact information */
    contact?: string
    /** Defines the verification type */
    type?: 'PHONE' | 'EMAIL'
    /** Defines the verification source */
    source?: 'LOGIN' | 'PASSWORD_RESET' | 'EMAIL_RESET' | 'ADMIN_LOGIN'
    behaviorVerifyInfo?: BehaviorVerifyDTO
  }
}
