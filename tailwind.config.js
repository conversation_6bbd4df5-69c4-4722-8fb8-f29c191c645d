const defaultTheme = require('tailwindcss/defaultTheme')

/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['"Inter var"', ...defaultTheme.fontFamily.sans],
      },
      fontSize: {
        xs: ['0.75rem', '1.25rem'],
        sm: ['0.875rem', '1.5rem'],
        base: ['1rem', '1.5rem'],
        lg: ['1.5rem', '2rem'],
        xl: ['2rem', '2.5rem'],
      },
      colors: {
        primary: 'rgb(var(--primary-raw) / <alpha-value>)',
        secondary: 'rgb(var(--secondary-raw) / <alpha-value>)',
        tertiary: 'rgb(var(--tertiary-raw) / <alpha-value>)',
        'neutral-50': 'rgb(var(--neutral-50-raw) / <alpha-value>)',
        'neutral-100': 'rgb(var(--neutral-100-raw) / <alpha-value>)',
        'neutral-200': 'rgb(var(--neutral-200-raw) / <alpha-value>)',
        'neutral-300': 'rgb(var(--neutral-300-raw) / <alpha-value>)',
        'neutral-400': 'rgb(var(--neutral-400-raw) / <alpha-value>)',
        'neutral-500': 'rgb(var(--neutral-500-raw) / <alpha-value>)',
        'neutral-600': 'rgb(var(--neutral-600-raw) / <alpha-value>)',
        'neutral-700': 'rgb(var(--neutral-700-raw) / <alpha-value>)',
        'neutral-800': 'rgb(var(--neutral-800-raw) / <alpha-value>)',
        'neutral-900': 'rgb(var(--neutral-900-raw) / <alpha-value>)',
        'neutral-950': 'rgb(var(--neutral-950-raw) / <alpha-value>)',
      },
      textColor: {
        primary: 'rgb(var(--neutral-900-raw))',
        'primary_on-brand': 'rgb(var(--neutral-950-raw))',
        secondary: 'rgb(var(--neutral-700-raw))',
        'secondary_on-brand': 'rgb(var(--neutral-200-raw))',
        tertiary: 'rgb(var(--neutral-500-raw))',
        'tertiary_on-brand': 'rgb(var(--neutral-400-raw))',
        quaternary: 'rgb(var(--neutral-300-raw))',
        brand: 'rgb(var(--primary-raw))',
        white: 'rgb(var(--neutral-50-raw))',
        disabled: 'rgb(var(--neutral-300-raw))',
        placeholder: 'rgb(var(--neutral-400-raw))',
        error: 'rgb(var(--color-red-raw))',
        warning: 'rgb(var(--color-orange-raw))',
        success: 'rgb(var(--color-green-raw))',
      },
      backgroundColor: {
        brand: 'rgb(var(--primary-raw))',
        primary: 'rgb(var(--neutral-100-raw))',
        secondary: 'rgb(var(--neutral-50-raw))',
        tertiary: 'rgb(var(--neutral-100-raw))',
        quaternary: 'rgb(var(--neutral-200-raw))',
        error: 'rgb(var(--color-red-raw))',
        warning: 'rgb(var(--color-orange-raw))',
        success: 'rgb(var(--color-green-raw))',
      },
      borderColor: {
        primary: 'rgb(var(--neutral-50-raw))',
        secondary: 'rgb(var(--neutral-200-raw))',
        tertiary: 'rgb(var(--neutral-300-raw))',
        disabled: 'rgb(var(--neutral-400-raw))',
        brand: 'rgb(var(--primary-raw))',
        error: 'rgb(var(--color-red-raw))',
        warning: 'rgb(var(--color-orange-raw))',
        success: 'rgb(var(--color-green-raw))',
      },
    },
  },
  plugins: [],
}
