<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload v-model:value="initImage" :drag="true" label="款式图" />
        <FormItem label="粘贴区域">
          <div class="grid grid-cols-2 gap-2">
            <div
              class="mode"
              :class="{ selected: pasteArea === 'PARTIAL' }"
              @click="pasteArea = 'PARTIAL'"
            >
              花型
            </div>
            <div
              class="mode"
              :class="{ selected: pasteArea === 'OVERALL' }"
              @click="pasteArea = 'OVERALL'"
            >
              面料
            </div>
          </div>
          <MyButton
            class="flex-shrink-0 mt-2"
            type="form"
            icon="import"
            :disabled="!initImage"
            @click="editVisible = true"
            >选择图案区域</MyButton
          >
        </FormItem>
        <PictureUpload
          v-model:value="elementImage"
          :drag="true"
          label="元素图"
        />
        <FormItem label="图案密度">
          <my-slider
            v-model="density"
            :min="1"
            :max="5"
            :step="1"
            show-tooltip
            show-stops
            :format-tooltip="
              value => {
                let tag = '超小'
                if (value > 1) {
                  tag = '小'
                }
                if (value > 2) {
                  tag = '中'
                }
                if (value > 3) {
                  tag = '大'
                }
                if (value > 4) {
                  tag = '超大'
                }
                return tag
              }
            "
          ></my-slider>
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount v-model:count="count" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>

  <PaintModal
    v-model:visible="editVisible"
    @confirm="maskImage = $event"
    :bottomImage="initImage || undefined"
    :maskImage="maskImage || undefined"
  />
</template>
<script setup lang="ts">
import { patternReplace } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, ref, watch } from 'vue'

const editVisible = ref(false)
const maskImage = ref<string | null | undefined>()
const initImage = ref<string | null | undefined>()
const elementImage = ref<string | null | undefined>()
const pasteArea = ref<'PARTIAL' | 'OVERALL'>('PARTIAL')
const density = ref(1)
const count = ref<number>(4)
const confirmLoading = ref(false)
const activeIndex = ref(0)

const artwork = useArtwork()
const userStore = useUserInfoStore()

const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!initImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传款式图')
    return
  }
  if (!elementImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传元素图')
    return
  }
  confirmLoading.value = true
  try {
    const res = await patternReplace({
      num: count.value,
      drawParam: {
        initImage: initImage.value,
        patternDensity: density.value,
        pasteArea: pasteArea.value,
        pasteAreaImage: maskImage.value ?? undefined,
        elementImage: elementImage.value,
      },
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    ElMessage.error(error.message || '出错了')
  }
}

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => initImage.value,
  () => {
    maskImage.value = null
  }
)

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    !!params.id && artwork.setCreatingArtworks([params])
    maskImage.value = params?.patternReplaceFields?.pasteAreaImage
    initImage.value = params?.patternReplaceFields?.initImage
    elementImage.value = params?.patternReplaceFields?.elementImage
    pasteArea.value = params?.patternReplaceFields?.pasteArea ?? 'PARTIAL'
    density.value = params?.patternReplaceFields?.patternDensity ?? 1
  },
  { deep: true, immediate: true }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss">
.mode {
  @apply text-primary font-semibold text-sm rounded-xl h-12 cursor-pointer border border-secondary border-solid flex items-center justify-center;

  &.selected {
    @apply border-brand text-brand;
  }
}
</style>
