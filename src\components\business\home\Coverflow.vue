<template>
  <div class="w-full flex items-center justify-center relative">
    <img
      src="@/assets/images/home/<USER>"
      alt=""
      class="swiper-button-prev"
    />
    <div class="swiper card-slider">
      <div class="swiper-wrapper">
        <div
          class="swiper-slide"
          v-for="(image, idx) in props.images"
          :key="idx"
        >
          <img :src="image" />
        </div>
      </div>
    </div>
    <img
      src="@/assets/images/home/<USER>"
      alt=""
      class="swiper-button-next"
    />
  </div>
</template>

<script setup lang="ts">
import Swiper from 'swiper'
import { Navigation, EffectCoverflow } from 'swiper/modules'
import 'swiper/swiper-bundle.css'
import { onMounted, ref } from 'vue'

const props = defineProps<{
  images: string[]
}>()

const swiper = ref<Swiper | null>(null)

onMounted(() => {
  // FIXME: 这里swiper官方有个bug：https://github.com/nolimits4web/swiper/issues/7172
  // 在切换快了之后会出现两边不对称的情况
  swiper.value = new Swiper('.card-slider', {
    modules: [Navigation, EffectCoverflow],
    effect: 'coverflow',
    centeredSlides: true,
    loop: true,
    grabCursor: true,
    slidesPerView: 'auto',
    slideToClickedSlide: true,
    loopAdditionalSlides: 3,
    coverflowEffect: {
      slideShadows: false,
      rotate: 0,
      stretch: 0,
      depth: 166,
      modifier: 2,
    },
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
  })
})
</script>

<style scoped>
.card-slider {
  @apply w-[1252px] overflow-hidden;
}

.swiper-slide {
  @apply w-[327.6px] h-[471.6px];

  img {
    @apply object-cover size-full;
  }
}
</style>
