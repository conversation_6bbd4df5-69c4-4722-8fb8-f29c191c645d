<script setup lang="ts">
import MyDialog from '@/components/ui/MyDialog.vue'
import { ref } from 'vue'
import ValidateCode from './ValidateCode.vue'
import ValidateEmail from './ValidateEmail.vue'
import { useUserInfoStore } from '@/store'

const { visible } = defineProps<{ visible: boolean }>()
const userStore = useUserInfoStore()
type ILoginType = 'code' | 'email'
const loginType = ref<ILoginType>('code')

const onTypeChange = (type: ILoginType) => {
  loginType.value = type
}

const onValidateEmail = () => {
  // if (!userStore.base?.email) {
  //   return ElMessage.error('未设置邮箱号')
  // }
  onTypeChange('email')
}
const onClose = () => {
  userStore.setValidateAccountInfo(null)
}
const onSuccess = () => {
  userStore.getUserInfo()
  onClose()
  userStore.setLoginType('')
}
</script>
<template>
  <MyDialog
    title="安全验证"
    needFillBackground
    :visible="visible"
    :width="480"
    :showFooter="false"
    @close="onClose"
  >
    <ValidateCode v-if="loginType === 'code'" @success="onSuccess" />
    <ValidateEmail v-else @success="onSuccess" />

    <div class="pt-8 flex justify-center gap-14 text-neutral-700 font-semibold">
      <a v-if="loginType !== 'email'" @click="onValidateEmail">邮箱验证</a>
      <a v-if="loginType !== 'code'" @click="onTypeChange('code')"
        >手机号验证</a
      >
    </div>
  </MyDialog>
</template>
