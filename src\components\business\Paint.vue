<script setup lang="ts">
import { uploadQiNiu } from '@/hooks/useUpload'
import Paint from '@super-puzzle/paint'
import { defineProps, useTemplateRef, reactive, watch, ref } from 'vue'

const { dialogOpened, bottomImage, maskImage } = defineProps<{
  dialogOpened: boolean
  bottomImage?: string
  maskImage?: string
}>()
const emit = defineEmits(['cancel', 'confirm'])
const uploadLoading = ref(false)

watch(
  () => dialogOpened,
  () => {
    if (!dialogOpened) return
    const p = new Paint(canvas.value!, cursor.value!)
    if (bottomImage) {
      p.loadBottomImage(bottomImage)
    }
    if (maskImage) {
      p.loadMaskImage(maskImage)
    }

    p.selectBrush()
    paintRef = p

    p.emitter.on('canvasUpdate', () => {
      if (!previewCanvasRef.value) return
      if (!previewBgCanvasRef.value) return
      const previewCanvas = previewCanvasRef.value
      const previewBgCanvas = previewBgCanvasRef.value
      const maskCanvas = p.layersManager.saveMaskCanvas(
        'rgba(0,0,0, 0)',
        [0, 0, 0, 255]
      )
      previewCanvas.width = maskCanvas.width
      previewCanvas.height = maskCanvas.height
      previewBgCanvas.width = maskCanvas.width
      previewBgCanvas.height = maskCanvas.height

      const bgCtx = previewBgCanvas.getContext('2d')!
      p.layersManager.renderBottomImage(bgCtx)
      bgCtx.fillStyle = 'rgba(0,0,0,0.5)'
      bgCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height)
      paintRef.setLineWidth(state.brushSize)
      const canvasCtx = previewCanvas.getContext('2d')!
      canvasCtx.clearRect(0, 0, previewCanvas.width, previewCanvas.height)
      p.layersManager.renderBottomImage(canvasCtx)
      canvasCtx.globalCompositeOperation = 'destination-in'
      canvasCtx.drawImage(maskCanvas, 0, 0)
    })
  }
)

const canvas = useTemplateRef('canvas')
const previewCanvasRef = useTemplateRef('previewCanvas')
const previewBgCanvasRef = useTemplateRef('previewBgCanvas')
const cursor = useTemplateRef('cursor')
let paintRef: Paint
const state = reactive({
  curTool: 'brush',
  brushSize: 30,
  eraseSize: 30,
})

function selectBrush() {
  paintRef.selectBrush()
  state.curTool = 'brush'
  console.log('brush', state.brushSize)
  paintRef.setLineWidth(state.brushSize)
}

function selectErase() {
  paintRef.selectErase()
  state.curTool = 'erase'
  console.log('erase', state.eraseSize)
  paintRef.setLineWidth(state.eraseSize)
}

function undo() {
  paintRef.undo()
}

function select() {
  paintRef.selectSelect()
  state.curTool = 'select'
}

/**
 * 判断图片是否经过涂抹（图片中含有白色像素块）
 * @param url
 * @returns
 */
function isEmptyMaskZoneImage(url: string) {
  console.log(url, '685')
  let resolve: (b: boolean) => void
  let reject: (reason?: any) => void
  let promise = new Promise<boolean>((ro, re) => {
    resolve = ro
    reject = re
  })
  const img = new Image()
  img.crossOrigin = 'Anonymous'
  img.onload = () => {
    const c = document.createElement('canvas')
    const w = img.naturalWidth
    const h = img.naturalHeight
    c.width = w
    c.height = h
    const ctx = c.getContext('2d')!

    // 启用抗锯齿
    ctx.imageSmoothingEnabled = true
    ctx.imageSmoothingQuality = 'high'

    // 线条设置
    ctx.lineCap = 'round' // 使线条末端圆润
    ctx.lineJoin = 'round' // 使线条转角圆润
    const drawWidth = img.naturalWidth
    const drawHeight = img.naturalHeight
    const drawX = (w - drawWidth) / 2
    const drawY = (h - drawHeight) / 2
    ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight)
    const oldData = ctx.getImageData(0, 0, w, h)
    console.log(oldData, '8687')
    let isEmpty = true
    for (let i = 0; i < oldData.data.length; i += 4) {
      const r = oldData.data[i]
      const g = oldData.data[i + 1]
      const b = oldData.data[i + 2]
      const a = oldData.data[i + 3]
      if (r + g + b + a === 255 * 4) {
        isEmpty = false
      }
    }

    resolve(isEmpty)
  }
  img.onerror = e => {
    reject(e)
  }
  img.src = url

  return promise
}
async function saveMask() {
  try {
    uploadLoading.value = true
    const res = await paintRef.saveMask()
    const file = new File([res], 'mask.png', { type: res.type })
    const { url } = await uploadQiNiu(new File([res!], 'mask.png'))
    const isEmpty = await isEmptyMaskZoneImage(url)
    if (isEmpty) {
      emit('cancel')
      emit('confirm', { file: null, url: null })
    } else {
      emit('confirm', { file, url })
      emit('cancel')
    }
  } catch (err) {
    console.error(err)
  } finally {
    uploadLoading.value = false
  }
}
</script>

<template>
  <div class="flex items-center justify-between">
    <div class="flex mb-6 select-none">
      <div
        class="cursor-pointer w-[76px] h-[68px] hover:text-brand flex flex-col items-center justify-center gap-1"
        @click="undo"
      >
        <MyIcon icon="undo" :size="24"></MyIcon>
        <div>撤销</div>
      </div>
      <div
        class="cursor-pointer w-[76px] h-[68px] hover:text-brand flex flex-col items-center justify-center gap-1"
        @click="paintRef.redo"
      >
        <MyIcon icon="redo" :size="24"></MyIcon>
        <div>重做</div>
      </div>
      <div
        :class="[
          'cursor-pointer w-[76px] h-[68px] hover:text-brand flex flex-col items-center justify-center gap-1',
          { '!text-brand': state.curTool === 'select' },
        ]"
        @click="select"
      >
        <MyIcon icon="move" :size="24"></MyIcon>
        <div>移动</div>
      </div>

      <el-popover
        trigger="click"
        placement="bottom"
        width="200"
        :show-arrow="false"
        popper-class="custom-popover-for-paint"
        :hide-after="1"
        :offset="4"
      >
        <template #reference>
          <div
            :class="[
              'cursor-pointer w-[76px] h-[68px] hover:text-brand flex flex-col items-center justify-center gap-1',
              { '!text-brand': state.curTool === 'brush' },
            ]"
            @click="selectBrush"
          >
            <MyIcon icon="brush" :size="24"></MyIcon>
            <div>画笔</div>
          </div>
        </template>
        <template #default>
          <my-slider
            :min="1"
            :max="50"
            v-model="state.brushSize"
            @change="
              (v: number) => {
                console.log(v)
                paintRef.setLineWidth(v)
              }
            "
          />
        </template>
      </el-popover>

      <el-popover
        trigger="click"
        placement="bottom"
        width="200"
        :show-arrow="false"
        popper-class="custom-popover-for-paint"
        :hide-after="1"
        :offset="4"
      >
        <template #reference>
          <div
            :class="[
              'cursor-pointer w-[76px] h-[68px] hover:text-brand flex flex-col items-center justify-center gap-1',
              { '!text-brand': state.curTool === 'erase' },
            ]"
            @click="selectErase"
          >
            <MyIcon icon="easera" :size="24"></MyIcon>
            <div>橡皮</div>
          </div>
        </template>
        <template #default>
          <my-slider
            :min="1"
            :max="50"
            v-model="state.eraseSize"
            @change="
              (v: number) => {
                console.log(v)
                paintRef.setLineWidth(v)
              }
            "
          />
        </template>
      </el-popover>
    </div>
    <div class="flex items-center gap-8">
      <MyButton @click="emit('cancel')">取消</MyButton>
      <MyButton type="primary" @click="saveMask">确定</MyButton>
    </div>
  </div>

  <div class="flex flex-1 gap-4 h-full flex-shrink-0 relative">
    <div class="absolute z-10" ref="cursor"></div>

    <div
      class="relative h-full flex-shrink-0 bg-neutral-100 rounded-3xl"
      style="width: calc(50% - 8px)"
    >
      <canvas ref="canvas" class="w-full h-full"></canvas>
    </div>
    <div
      class="relative h-full flex-shrink-0 bg-neutral-100 rounded-3xl overflow-hidden"
      style="width: calc(50% - 8px)"
    >
      <canvas
        ref="previewBgCanvas"
        class="w-full h-full absolute top-0 left-0 bottom-0 right-0"
      ></canvas>
      <canvas
        ref="previewCanvas"
        class="w-full h-full absolute top-0 left-0 bottom-0 right-0"
      ></canvas>
    </div>
  </div>
</template>

<style>
.custom-popover-for-paint {
  --el-popover-border-radius: 1000px;
  --el-box-shadow-light: 0px 8px 24px 0px rgba(0, 0, 0, 0.08);
  @apply !bg-neutral-50;
}
</style>

<style scoped lang="scss">
.el-popover {
  --el-popover-border-radius: 1000px;
}
.el-slider {
  :deep(.el-slider__bar) {
    @apply bg-primary;
  }
}
</style>
