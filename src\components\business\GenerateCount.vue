<template>
  <div class="flex gap-2">
    <div
      v-for="(item, index) in props.counts"
      :key="index"
      class="cursor-pointer flex-1 h-12 bg-neutral-200 rounded-xl p-px"
      :class="{ active: item === props.count }"
      @click="handleClick(item)"
    >
      <div
        class="text-[16px] flex h-full items-center justify-center bg-white rounded-[10px]"
      >
        <div>{{ item }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    counts?: number[]
    count?: number
  }>(),
  {
    counts: () => [1, 2, 4],
    count: 2,
  }
)

const emit = defineEmits(['update:count', 'onChangeSelect'])

const handleClick = (item: number) => {
  emit('onChangeSelect', item)
}
</script>

<style scoped>
.active {
  @apply p-[2px];
  background: linear-gradient(
    92.76deg,
    #fadff0 0.96%,
    #f6ccf1 41.3%,
    #aad7fe 97.7%
  );
  color: #f6ccf1;
}
</style>
