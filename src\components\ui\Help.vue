<template>
  <el-popover
    placement="top-start"
    trigger="hover"
    :content="content"
    v-bind="$attrs"
  >
    <template #reference>
      <el-icon><QuestionFilled /></el-icon>
    </template>
  </el-popover>
</template>

<script setup lang="ts">
interface IProps {
  content: string
}
const { content } = defineProps<IProps>()
</script>

<style scoped lang="scss">
.icon {
  color: red;
}
</style>
