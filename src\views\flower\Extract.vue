<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          @onUrlChange="getPatternExtraction"
        />
        <FormItem label="画面描述（提取元素）">
          <MyTextarea
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <MyButton
          type="form"
          icon="import"
          :disabled="!imageResolverData?.length"
          @click="imageResolverVisible = true"
          >从图像解析选择输入</MyButton
        >
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
  <ImageResolverModal
    v-model:visible="imageResolverVisible"
    :data="imageResolverData"
    :prompt="prompt"
    @confirm="onImageResolverConfirm"
  />
</template>
<script setup lang="ts">
import { patternExtraction, prePatternExtraction } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

const uploadImage = ref<string | null | undefined>()
const prompt = ref('')
const confirmLoading = ref(false)
const imageResolverVisible = ref(false)
const imageResolverData = ref<string[]>([])
const activeIndex = ref(0)

const artwork = useArtwork()
const userStore = useUserInfoStore()

const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }
  try {
    confirmLoading.value = true
    const res = await patternExtraction({
      num: 1,
      drawParam: {
        imageUrl: uploadImage.value,
        prompt: prompt.value,
      },
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    ElMessage.error(error.message || '出错了')
  }
}

const onImageResolverConfirm = (keywords: string[]) => {
  prompt.value = keywords.join(',')
  imageResolverVisible.value = false
}

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

const getPatternExtraction = async (url?: string) => {
  if (!url) return
  imageResolverData.value = []
  prompt.value = ''
  try {
    const res = await prePatternExtraction({
      imageUrl: url,
    })
    imageResolverData.value = res?.data?.tags ?? []
  } catch (error) {
    ElMessage.error('图像解析失败')
    imageResolverData.value = []
  }
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    getPatternExtraction(params?.flowerPatternExtractionFields?.inImgUrl)
    !!params.id && artwork.setCreatingArtworks([params])
    uploadImage.value = params?.flowerPatternExtractionFields?.inImgUrl
    prompt.value = params?.flowerPatternExtractionFields?.prompt || ''
  },
  { deep: true, immediate: true }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss"></style>
