<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full" style="position: relative">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <!-- <FormItem label="画面描述">
          <MyTextarea1
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem> -->
        <FormItem label="重绘强度">
          <MySlider
            :max="1"
            :min="0"
            v-model="sliderRedraw"
            show-input
            :step="0.01"
          />
        </FormItem>
        <FormItem label="迭代步数">
          <MySlider :max="60" :min="1" v-model="sliderStep" show-input />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { img2img } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork } from '@/store'
import { onBeforeUnmount, ref, watch } from 'vue'
import { toBlob } from '@/utils'
import { ElMessage } from 'element-plus'
import { useUserInfoStore } from '@/store'
const uploadImage = ref<string | null | undefined>()
const prompt = ref('')
const confirmLoading = ref(false)
const activeIndex = ref(0)
const originImg = ref()
const artwork = useArtwork()
const userStore = useUserInfoStore()
const sliderRedraw = ref(1)
const sliderStep = ref(25)
const count = ref(2)
const selectChange = (e: number) => {
  count.value = e
}
// const callBackFun = e => {
//   originImg.value = e
// }
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  // if (!prompt.value) {
  //   ElMessage.closeAll()
  //   ElMessage.error('请输入画面描述')
  //   return
  // }
  // if (prompt.value == '提示词生成中...' || prompt.value == '提示词润色中...') {
  //   ElMessage.closeAll()
  //   ElMessage.error('请等待提示词生成完成')
  //   return
  // }
  try {
    confirmLoading.value = true
    const response = await toBlob(uploadImage.value)
    originImg.value = response
    let formData = new FormData()
    formData.append('prompt', prompt.value)
    formData.append('image_load', originImg.value)
    formData.append('image_url', String(uploadImage.value))
    formData.append('steps', String(sliderStep.value))
    formData.append('n_iter', String(count.value))
    formData.append('denoise', String(sliderRedraw.value))
    const res = await img2img(formData)
    confirmLoading.value = false
    activeIndex.value = 0
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}
watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.change_head_plusFields?.imageUrl
    prompt.value = params?.change_head_plusFields?.prompt || ''
    sliderRedraw.value = params?.change_head_plusFields?.denoise
    sliderStep.value = params?.change_head_plusFields?.steps
    count.value = params?.change_head_plusFields?.n_iter
  }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss"></style>
