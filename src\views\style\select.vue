<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full" style="position: relative">
        <FormItem label="画面描述">
          <MyTextarea1
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
            showImgPrompt
          />
        </FormItem>
        <FormItem label="图像宽度">
          <MySlider :max="2048" :min="512" v-model="sliderWidth" show-input />
        </FormItem>
        <FormItem label="图像高度">
          <MySlider :max="2048" :min="512" v-model="sliderHeight" show-input />
        </FormItem>
        <FormItem label="迭代步数">
          <MySlider :max="60" :min="1" v-model="sliderStep" show-input />
        </FormItem>
        <FormItem label="客户选择">
          <MySelect
            v-model="selectVal"
            :list="customList"
            @onChangeSelect="selectCustomer"
          />
        </FormItem>
        <FormItem label="款式选择">
          <MySelect v-model="selectVal1" :list="styleList" />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
      </div>
    </template>
    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { styleCloth } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, ref, watch } from 'vue'
const prompt = ref('')
const sliderHeight = ref(1024)
const sliderWidth = ref(768)
const sliderStep = ref(25)
const count = ref(4)
const selectVal = ref('CAMILLA')
const selectVal1 = ref('连衣裙')
const customList = [
  { label: '某客户', value: 'CAMILLA' },
  { label: '某客户B', value: 'BIMBA Y LOLA' },
  { label: '2026印花款', value: 'pv展_印花' },
  { label: '2026丝绒款', value: 'pv展_丝绒' },
  // { label: '某客户C', value: '印花款' },
  // { label: 'MAJE', value: 'MAJE' },
  // { label: 'MINT VELVET', value: 'MINT VELVET' },
  // { label: 'HOBBS', value: 'HOBBS' },
  // { label: 'MAX MARA', value: 'MAX MARA' },
  // { label: 'ZIMMERMANN', value: 'ZIMMERMANN' },
  // { label: 'APG&Co', value: 'APG&Co' },
  // { label: 'CAMILLA&MARC', value: 'CAMILLA&MARC' },
]
// const styleList = ref([
//   { label: '连衣裙', value: '连衣裙' },
//   { label: '衬衫', value: '衬衫' },
//   { label: '裤子', value: '裤子' },
//   { label: '夹克', value: '夹克' },
//   { label: '连体裤', value: '连体裤' },
// ])
const styleList = ref([
  { label: '连衣裙', value: '连衣裙' },
  // { label: '衬衫', value: '衬衫' },
  // { label: '裤子', value: '裤子' },
  // { label: '夹克', value: '夹克' },
  // { label: '连体裤', value: '连体裤' },
])
const confirmLoading = ref(false)
const activeIndex = ref(0)
const selectChange = (e: number) => {
  count.value = e
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}
const selectCustomer = (e: string) => {
  // selectVal.value = e
  if (e != 'BIMBA Y LOLA') {
    styleList.value = [{ label: '连衣裙', value: '连衣裙' }]
    selectVal1.value = '连衣裙'
  } else {
    styleList.value = [
      { label: '连衣裙', value: '连衣裙' },
      { label: '衬衫', value: '衬衫' },
      { label: '裤子', value: '裤子' },
      { label: '夹克', value: '夹克' },
      { label: '连体裤', value: '连体裤' },
    ]
  }
}
const artwork = useArtwork()
const userStore = useUserInfoStore()

const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }
  if (prompt.value == '提示词生成中...' || prompt.value == '提示词润色中...') {
    ElMessage.closeAll()
    ElMessage.error('请等待提示词生成完成')
    return
  }
  try {
    confirmLoading.value = true
    let formData = new FormData()
    formData.append('prompt', prompt.value)
    formData.append('width', String(sliderWidth.value))
    formData.append('height', String(sliderHeight.value))
    formData.append('steps', String(sliderStep.value))
    formData.append('customer_name', selectVal.value)
    formData.append('style_name', selectVal1.value)
    formData.append('n_iter', String(count.value))
    const res = await styleCloth(formData)
    confirmLoading.value = false
    activeIndex.value = 0
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    prompt.value = params?.style_clothFields?.prompt || ''
    count.value = params?.style_clothFields?.n_iter
    selectVal.value = params?.style_clothFields?.customer_name
    selectVal1.value = params?.style_clothFields?.style_name
    sliderWidth.value = params?.style_clothFields?.width
    sliderHeight.value = params?.style_clothFields?.height
    sliderStep.value = params?.style_clothFields?.steps
  }
  // { deep: true, immediate: true }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss"></style>