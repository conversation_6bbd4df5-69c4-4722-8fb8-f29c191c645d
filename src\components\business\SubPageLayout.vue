<template>
  <div class="flex h-full">
    <div class="w-[360px] border-r flex flex-col relative">
      <div class="p-6 w-full pb-0">
        <slot name="nav"></slot>
      </div>
      <div
        class="p-6 w-full overflow-y-auto pt-0"
        style="height: calc(100% - 96px)"
      >
        <slot name="form"></slot>
      </div>
      <div class="absolute bottom-6 left-6 right-6">
        <MyButton
          type="primary"
          class="w-full"
          :loading="props.confirmLoading"
          @click="handleConfirm"
        >
          立即生成
          <div
            class="bg-secondary text-xs rounded-full px-3 py-1/2 text-primary/80 ml-2"
            v-if="userStore.base?.drawingTimes"
          >
            剩余次数 {{ userStore.base?.drawingTimes }} 次
          </div>
        </MyButton>
      </div>
    </div>
    <div class="flex-1 overflow-auto content-shadow">
      <slot></slot>
    </div>
    <HistoryBar />
  </div>
</template>
<script setup lang="ts">
import MyButton from '@/components/ui/MyButton.vue'
import { useAuth } from '@/hooks/useAuth'
import { useUserInfoStore } from '@/store'

const props = defineProps<{
  confirmLoading?: boolean
}>()
const emit = defineEmits(['confirm'])
const { validateLogin } = useAuth()
const userStore = useUserInfoStore()

const handleConfirm = () => {
  if (props.confirmLoading) return
  if (!validateLogin()) return
  emit('confirm')
}
</script>

<style scoped lang="scss">
.content-shadow {
  box-shadow: -8px 0px 16px 0px hsla(0, 0%, 0%, 0.04) inset,
    8px 0px 16px 0px hsla(0, 0%, 0%, 0.04) inset;
}
</style>
