<template>
  <MyDialog
    :visible="visible"
    :width="'90%'"
    :showFooter="false"
    :showHeader="false"
    top="0"
    @update:visible="emits('update:visible', $event)"
  >
    <div
      class="flex items-stretch justify-between h-full"
      v-if="!!detail?.id"
      style="height: calc(100vh - 64px)"
    >
      <div class="flex-1 bg-neutral-100 rounded-3xl h-full overflow-hidden">
        <MyImage
          v-if="!!detail?.result?.url"
          :src="detail?.result?.url"
          class="w-full h-full flex items-center justify-center"
        />
        <div
          class="w-full h-full flex items-center justify-center text-center"
          v-else
        >
          <div>
            <MyIcon icon="Warning" :size="42" class="text-tertiary" />
            <div class="text-tertiary text-sm mt-2.5">生成失败</div>
          </div>
        </div>
      </div>
      <div class="w-[440px] ml-6 relative">
        <div class="flex items-center justify-between mb-8">
          <div class="text-[#23262F] text-[28px] leading-10 font-semibold">
            作品详情
          </div>
          <div
            class="border-2 border-neutral-300 rounded-full w-10 h-10 flex justify-center items-center ml-6 cursor-pointer"
            @click="
              () => {
                emits('update:visible', false)
                emits('update:detail', null)
              }
            "
          >
            <MyIcon icon="close" :size="24" />
          </div>
        </div>
        <div
          class="mb-8"
          v-if="
            !detail?.style_clothFields &&
            detail?.change_head_plusFields?.headType !== '文生图'
          "
        >
          <div class="text-sm font-semibold text-primary mb-2">参考图</div>
          <div class="p-4 rounded-xl border border-neutral-200">
            <!-- 花型提取 -->
            <MyImage
              v-if="detail?.flowerPatternExtractionFields?.inImgUrl"
              :src="detail?.flowerPatternExtractionFields?.inImgUrl"
              class="w-[132px] h-[132px] rounded-md"
            />
            <!-- 图层分离 -->
            <MyImage
              v-if="detail?.layerSeparationFields?.inImgUrl"
              :src="detail?.layerSeparationFields?.inImgUrl"
              class="w-[132px] h-[132px] rounded-md"
            />
            <!-- 面料花型创作 -->
            <MyImage
              v-if="detail?.fabricPatternCreationFields?.initImage"
              :src="detail?.fabricPatternCreationFields?.initImage"
              class="w-[132px] h-[132px] rounded-md"
            />
            <!-- 四方连续 -->
            <MyImage
              v-if="detail?.patternCycleFields?.initImage"
              :src="detail?.patternCycleFields?.initImage"
              class="w-[132px] h-[132px] rounded-md"
            />
            <!-- 款式延伸 -->
            <MyImage
              v-if="detail?.styleExtensionFields?.initImage"
              :src="detail?.styleExtensionFields?.initImage"
              class="w-[132px] h-[132px] rounded-md"
            />
            <!-- 以款换面料 -->
            <MyImage
              v-if="detail?.fabricReplaceFields?.styleImageUrl"
              :src="detail?.fabricReplaceFields?.styleImageUrl"
              class="w-[132px] h-[132px] rounded-md"
            />
            <!-- 图案粘贴 -->
            <MyImage
              v-if="detail?.patternReplaceFields?.initImage"
              :src="detail?.patternReplaceFields?.initImage"
              class="w-[132px] h-[132px] rounded-md"
            />
            <!-- 特殊工艺替换 -->
            <MyImage
              v-if="detail?.change_head_plusFields?.imageUrl"
              :src="detail?.change_head_plusFields?.imageUrl"
              class="w-[132px] h-[132px] rounded-md"
            />
            <!-- 特殊工艺替换 -->
            <MyImage
              v-if="detail?.upscale_img4Fields?.imageUrl"
              :src="detail?.upscale_img4Fields?.imageUrl"
              class="w-[132px] h-[132px] rounded-md"
            />
            <MyImage
              v-if="
                detail?.change_head_plusFields?.imageClothesUrl &&
                detail?.change_head_plusFields?.headType !== '模特换装'
              "
              :src="detail?.change_head_plusFields?.imageClothesUrl"
              class="w-[132px] h-[132px] rounded-md"
            />
          </div>
        </div>
        <div class="mb-8" v-if="detail?.flowerPatternExtractionFields?.prompt">
          <div class="text-sm font-semibold text-primary mb-2">画面描述</div>
          <div class="p-4 rounded-xl border border-neutral-200">
            {{ detail?.flowerPatternExtractionFields?.prompt }}
          </div>
        </div>
        <div class="mb-8">
          <div class="text-sm font-semibold text-primary mb-2">其他</div>
          <div class="p-4 rounded-xl border border-neutral-200">
            <div class="item" v-if="detail?.layerSeparationFields?.numColors">
              <span>颜色数量（分离层数）</span
              ><span>{{ detail?.layerSeparationFields?.numColors }}</span>
            </div>
            <div class="item">
              <span>创作时间</span
              ><span>{{ toYMDHms(detail?.completeTime!) }}</span>
            </div>
            <div class="item">
              <span>图像格式</span><span>{{ detail?.result?.type }}</span>
            </div>
          </div>
        </div>
        <div
          class="bg-neutral-100 rounded-xl px-4 py-3 flex items-center justify-around absolute left-0 bottom-0 w-full"
        >
          <div
            class="text-neutral-700 text-xs cursor-pointer outline-none"
            @click="
              () => {
                ElMessageBox.confirm('确认删除作品吗？', '删除作品', {
                  confirmButtonText: '确认删除',
                  cancelButtonText: '取消',
                  confirmButtonClass: 'myMessageBoxConfirmButton',
                  cancelButtonClass: 'myMessageBoxCancelButton',
                  customClass: 'myMessageBox',
                }).then(() => {
                  onDelete(detail?.id)
                })
              }
            "
          >
            <div><MyIcon icon="delete" :size="24" /></div>
            <div>删除</div>
          </div>

          <el-dropdown
            placement="top"
            popper-class="myDropdown"
            @command="handleTransferCommand"
            v-if="
              // 增加png以外图片的发送功能，在这里增加类型

              !detail?.layerSeparationFields?.inImgUrl
            "
          >
            <div class="text-neutral-700 text-xs cursor-pointer outline-none">
              <div><MyIcon icon="transfer" :size="24" /></div>
              <div>发送</div>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :command="{ path: '/extension', url: detail?.result?.url }"
                  >局部重绘</el-dropdown-item
                >
                <el-dropdown-item
                  :command="{ path: '/craft', url: detail?.result?.url }"
                  >特殊工艺替换</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="
                    detail?.change_head_plusFields?.headType !== '灵感融合' &&
                    detail?.change_head_plusFields?.headType !== '涂抹换头' &&
                    detail?.change_head_plusFields?.headType !== '自动换头' &&
                    detail?.change_head_plusFields?.headType !== '模特换装' &&
                    detail?.upscale_img4Fields?.headType !== '分辨率提升' &&
                    detail?.change_head_plusFields?.headType !== '智能抠图'
                  "
                  :command="{ path: '/model/swap', url: detail?.result?.url }"
                  >模特换装</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="
                    detail?.change_head_plusFields?.headType == '局部重绘' ||
                    detail?.style_clothFields?.headType == '风格选择'
                  "
                  :command="{
                    path: '/extension/style',
                    url: detail?.result?.url,
                  }"
                  >灵感融合</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="
                    detail?.change_head_plusFields?.headType == '文生图' ||
                    detail?.change_head_plusFields?.headType == '图生图'
                  "
                  :command="{
                    path: '/model',
                    url: detail?.result?.url,
                  }"
                  >模特换头</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="
                    detail?.change_head_plusFields?.headType !== '以款换面料' &&
                    detail?.change_head_plusFields?.headType !== '局部重绘' &&
                    detail?.change_head_plusFields?.headType !== '涂抹换头' &&
                    detail?.change_head_plusFields?.headType !== '自动换头' &&
                    detail?.change_head_plusFields?.headType !== '灵感融合' &&
                    detail?.change_head_plusFields?.headType !== '模特换装' &&
                    detail?.upscale_img4Fields?.headType !== '分辨率提升' &&
                    detail?.change_head_plusFields?.headType !== '智能抠图'
                  "
                  :command="{
                    path: '/craft/fabricReplace',
                    url: detail?.result?.url,
                  }"
                  >以款换面料</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="
                    detail?.change_head_plusFields?.headType == '文生图' ||
                    detail?.change_head_plusFields?.headType == '图生图' ||
                    detail?.style_clothFields?.headType == '风格选择'
                  "
                  :command="{
                    path: '/image',
                    url: detail?.result?.url,
                  }"
                  >智能抠图</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="
                    detail?.change_head_plusFields?.headType == '文生图' ||
                    detail?.change_head_plusFields?.headType == '图生图' ||
                    detail?.style_clothFields?.headType == '风格选择'
                  "
                  :command="{
                    path: '/image/resup',
                    url: detail?.result?.url,
                  }"
                  >分辨率提升</el-dropdown-item
                >
                <!-- <template v-for="item in SEED_OPTIONS" :key="item.key">
                  <el-dropdown-item
                    v-if="!item.children?.length"
                    :command="item"
                    >{{ item.label }}</el-dropdown-item
                  >
                  <el-dropdown
                    placement="right-end"
                    popper-class="myDropdown"
                    :teleported="false"
                    @command="handleTransferCommand"
                    v-else
                  >
                    <div class="el-dropdown-menu__item w-full">
                      <div class="w-full flex items-center justify-between">
                        <div>{{ item.label }}</div>
                        <MyIcon icon="Arrow-right" :size="20" class="!-mr-1" />
                      </div>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <template v-for="o in item.children" :key="o.key">
                          <el-dropdown-item
                            v-if="!o?.children?.length"
                            :command="o"
                            >{{ o.label }}</el-dropdown-item
                          >
                          <el-dropdown
                            placement="right-end"
                            popper-class="myDropdown"
                            :teleported="false"
                            @command="handleTransferCommand"
                            v-else
                          >
                            <div class="el-dropdown-menu__item w-full">
                              <div
                                class="w-full flex items-center justify-between"
                              >
                                <div>{{ o.label }}</div>
                                <MyIcon
                                  icon="Arrow-right"
                                  :size="20"
                                  class="!-mr-1"
                                />
                              </div>
                            </div>
                            <template #dropdown>
                              <el-dropdown-menu>
                                <el-dropdown-item
                                  v-for="oo in o.children"
                                  :key="oo.key"
                                  :command="oo"
                                  >{{ oo.label }}</el-dropdown-item
                                >
                              </el-dropdown-menu>
                            </template>
                          </el-dropdown>
                        </template>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template> -->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dropdown
            placement="top"
            @command="handleCommand"
            popper-class="myDropdown"
            v-if="!!detail?.result?.url"
          >
            <div class="text-neutral-700 text-xs cursor-pointer outline-none">
              <div><MyIcon icon="download" :size="24" /></div>
              <div>下载</div>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in menus"
                  :command="item.url"
                  :key="item.url"
                  >{{ item.type }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { batchDeleteTaskUnitsByIds, QiNiuImgUpload } from '@/clients/api/zuohua'
import { SEED_OPTIONS } from '@/config'
import { useArtwork } from '@/store'
import { getFileExtension, toYMDHms } from '@/utils'
import { xhrDownload } from '@/utils/file'
import { ElMessage, ElMessageBox } from 'element-plus'
import { omit, upperCase } from 'lodash'
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useImgwork } from '@/store'
const { visible, detail } = defineProps<{
  visible: boolean
  detail?: API.TaskUnitDetailVO | null
}>()

const router = useRouter()
const useImg = useImgwork()
const artwork = useArtwork()

const menus = computed(() => {
  return [omit(detail?.result, 'extra'), ...(detail?.result?.extra ?? [])]
})

const handleCommand = async (url: string) => {
  if (url.includes('?Expires=')) {
    try {
      let formData = new FormData()
      formData.append('image_url', url)
      const res = await QiNiuImgUpload(formData)
      if (res.data) {
        xhrDownload(res.data)
      }
    } catch (error: any) {
      // ElMessage.error(error.message || '出错了')
    }
  } else {
    xhrDownload(url)
  }
}

const handleTransferCommand = item => {
  router.replace({
    path: item.path,
  })
  if (item.path === '/craft') {
    useImg.setUpdateUrlCraft(item.url)
  } else if (item.path === '/extension') {
    useImg.setUpdateUrl(item.url)
  } else if (item.path === '/model/swap') {
    useImg.setUpdateUrlModelSwap(item.url)
  } else if (item.path === '/model') {
    useImg.setUpdateUrlModel(item.url)
  } else if (item.path === '/extension/style') {
    useImg.setUpdateUrlStyle(item.url)
  } else if (item.path === '/image') {
    useImg.setUpdateUrlImage(item.url)
  } else if (item.path === '/image/resup') {
    useImg.setUpdateUrlResup(item.url)
  } else {
    useImg.setUpdateUrlFabric(item.url)
  }
  // switch (item.key) {
  //   case 'flower-layer-initImage':
  //     artwork.setDrawParams({
  //       layerSeparationFields: {
  //         inImgUrl: detail?.result?.url,
  //       },
  //     })
  //     break
  //   case 'flower-extract-initImage':
  //     artwork.setDrawParams({
  //       flowerPatternExtractionFields: {
  //         inImgUrl: detail?.result?.url,
  //       },
  //     })
  //     break
  //   case 'creativity-repeat-initImage':
  //     artwork.setDrawParams({
  //       patternCycleFields: {
  //         initImage: detail?.result?.url,
  //       },
  //     })
  //     break
  //   case 'creativity-extend-initImage':
  //     artwork.setDrawParams({
  //       styleExtensionFields: {
  //         initImage: detail?.result?.url,
  //       },
  //     })
  //     break
  //   case 'creativity-flower-initImage':
  //     artwork.setDrawParams({
  //       fabricPatternCreationFields: {
  //         initImage: detail?.result?.url,
  //       },
  //     })
  //     break
  //   case 'style-extension-styleImage':
  //     artwork.setDrawParams({
  //       fabricReplaceFields: {
  //         styleImageUrl: detail?.result?.url,
  //       },
  //     })
  //     break
  //   case 'style-extension-fabricImage':
  //     artwork.setDrawParams({
  //       fabricReplaceFields: {
  //         fabricImageUrl: detail?.result?.url,
  //       },
  //     })
  //     break
  //   case 'image-paste-initImage':
  //     artwork.setDrawParams({
  //       patternReplaceFields: {
  //         initImage: detail?.result?.url,
  //       },
  //     })
  //     break
  //   case 'image-paste-elementImage':
  //     artwork.setDrawParams({
  //       patternReplaceFields: {
  //         elementImage: detail?.result?.url,
  //       },
  //     })
  //     break

  //   default:
  //     break
  // }
  // router.push({ path: item.path, query: { url: item.url } })
  emits('update:visible', false)
}

const onDelete = async (id?: number) => {
  if (!id) return
  const res = await batchDeleteTaskUnitsByIds({
    drawTaskUnitIds: [id],
  })
  if (res?.data) {
    ElMessage.success('删除成功')
    artwork.setUpdateHistory(true)
    artwork.setCreatingArtworks([])
    artwork.setDrawParams(null)
    emits('update:visible', false)
    emits('delete')
  }
}

const emits = defineEmits(['update:visible', 'update:detail', 'delete'])
</script>

<style scoped lang="scss">
.item {
  @apply flex items-center justify-between;
  > span {
    @apply text-sm text-primary font-semibold;
    + span {
      @apply font-normal text-secondary;
    }
  }
  + .item {
    @apply mt-4;
  }
}
</style>
