import { createOssUploadToken } from '@/clients/api/resource'
import { UploadRawFile, UploadRequestOptions } from 'element-plus'
import { ref } from 'vue'
import * as qiniu from 'qiniu-js'
import { useAuth } from './useAuth'

interface OptionsProps {
  onUrlChange?: (url: string, uploadFile: UploadRawFile) => void
  /** 大小限制，单位 M */
  sizeLimit?: number
  fileType?: string[]
}

export const uploadQiNiu = async (file: File, options?: any) => {
  const res = await createOssUploadToken({
    originalFileName: file.name,
  })
  const token = res?.data?.token!
  const QNUPLOAD = res?.data?.region
    ? `upload-${res.data.region}.qiniup.com`
    : `upload-z0.qiniup.com`

  const observable = qiniu.upload(file, undefined, token, undefined, {
    uphost: QNUPLOAD,
  })
  const p = new Promise((resolve, reject) => {
    observable.subscribe({
      next: options?.onProgress,
      error(err: any) {
        reject(err)
      },
      complete(res: any) {
        resolve(res)
      },
    })
  })
  const uploadRes: any = await p
  return {
    url: uploadRes.baseUrl + '/' + uploadRes.key,
  }
}

export function useUpload(options: OptionsProps) {
  const { validateLogin } = useAuth()
  const {
    onUrlChange,
    sizeLimit = 50,
    fileType = ['image/jpeg', 'image/png'],
  } = options || {}
  const loading = ref(false)
  const progress = ref(0)
  const isError = ref(false)

  const beforeUpload = async (rawFile: UploadRawFile) => {
    console.log(rawFile, 'beforeUpload.file')
    if (!validateLogin()) return Promise.reject()
    if (!!fileType?.length && !fileType.includes(rawFile.type)) {
      ElMessage.closeAll()
      ElMessage.error('不支持该文件类型')
      return Promise.reject()
    }
    const isLimit = rawFile.size / 1024 / 1024 < sizeLimit
    if (!isLimit) {
      ElMessage.closeAll()
      ElMessage.error(`请上传 ${sizeLimit}M 内的文件`)
      return Promise.reject()
    }
  }
  const customUpload = async (options: UploadRequestOptions) => {
    const { file } = options
    loading.value = true
    try {
      const { url } = await uploadQiNiu(options.file, {
        onProgress({
          total,
        }: {
          total: {
            percent: number
          }
        }) {
          progress.value = Math.floor(total.percent || 0)
        },
      })

      isError.value = false
      onUrlChange?.(url, file)
      progress.value = 0
      return url
    } catch (err) {
      isError.value = false
      console.log(err, 'customRequest.err')
      ElMessage.closeAll()
      ElMessage.error('上传异常，请稍后重试')
    } finally {
      loading.value = false
    }
  }
  return { loading, beforeUpload, customUpload, isError, progress }
}
