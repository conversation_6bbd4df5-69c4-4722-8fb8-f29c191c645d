@import './tailwind.css';

html {
  font-size: 16px;
}

body {
  color: #141416;
  background: #fff;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Roboto,
    'Helvetica Neue', Helvetica, SimSun, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
  -webkit-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  overscroll-behavior-y: none;
}

.selectItems {
  position: absolute;
  right: 0;
  border-radius: 8px;
  width: 38px;
  height: 20px;
  border: 1px solid #d4d4d4;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
  margin-top: 2px;
}

* {
  box-sizing: border-box;
}

a {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

.login-form.el-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-form-item__label {
    @apply text-primary text-xs;

    &::before {
      display: none;
    }
  }

  .el-input .el-input__wrapper {
    border-width: 2px;
  }

  .is-error .el-input .el-input__wrapper:not(.is-focus) {
    @apply border-error;
  }

  .is-error .el-input .el-input__inner {
    @apply text-error;
  }

  .el-form-item__error {
    @apply text-error text-xs;
  }
}

.el-pagination {
  @apply flex gap-1;

  .btn-prev,
  .btn-next {
    @apply border border-secondary border-solid rounded-md hover:text-brand;

    &[disabled] {
      @apply text-tertiary border-tertiary;
    }
  }

  .el-pager {
    @apply flex gap-1;

    li {
      @apply border border-secondary border-solid rounded-md hover:text-brand;

      &.is-active {
        @apply border-brand text-brand;
      }
    }
  }
}