<svg width="1920" height="1051" viewBox="0 0 1920 1051" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_940_12200)">
<g filter="url(#filter0_f_940_12200)">
<path d="M1752.51 749.505C1950.85 400.853 1590.41 268.565 1554.17 232.305C1812.65 -140.653 2449.49 232.305 2323.24 605.263C2196.98 978.221 1554.17 1098.16 1752.51 749.505Z" fill="#D9FDFF"/>
</g>
<g filter="url(#filter1_f_940_12200)">
<path d="M1051.86 557.614C1208.32 282.707 923.987 178.399 895.396 149.809C1099.3 -144.264 1601.67 149.809 1502.08 443.881C1402.48 737.953 895.396 832.522 1051.86 557.614Z" fill="#6DB3F8" fill-opacity="0.21"/>
</g>
<g filter="url(#filter2_f_940_12200)">
<path d="M2008.74 1008.37C1896.14 938.255 1636.26 893.029 1497.52 1273.03C1324.09 1748.04 2519.97 1446.46 2008.74 1008.37Z" fill="#FACFE9" fill-opacity="0.62"/>
</g>
<g filter="url(#filter3_f_940_12200)">
<path d="M233.175 11.7572C120.576 -58.356 -139.307 -103.581 -278.048 276.423C-451.474 751.429 744.398 449.85 233.175 11.7572Z" fill="#FACFE9" fill-opacity="0.3"/>
</g>
</g>
<defs>
<filter id="filter0_f_940_12200" x="1111.17" y="-365.179" width="1671.45" height="1765.08" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="221.5" result="effect1_foregroundBlur_940_12200"/>
</filter>
<filter id="filter1_f_940_12200" x="601.396" y="-266" width="1207.6" height="1281.14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="147" result="effect1_foregroundBlur_940_12200"/>
</filter>
<filter id="filter2_f_940_12200" x="1157.97" y="638.8" width="1301.06" height="1197.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="161.1" result="effect1_foregroundBlur_940_12200"/>
</filter>
<filter id="filter3_f_940_12200" x="-617.6" y="-357.811" width="1301.06" height="1197.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="161.1" result="effect1_foregroundBlur_940_12200"/>
</filter>
<clipPath id="clip0_940_12200">
<rect width="1920" height="1051" fill="white" transform="matrix(-1 0 0 1 1920 0)"/>
</clipPath>
</defs>
</svg>
