.headerWrap {
  height: 80px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;

  .headerBgLeft,
  .headerBgRight {
    flex: 1;
    height: 100%;
  }
  .headerBgRight {
    background: linear-gradient(20deg, #fff 43.43%, #fbdef5 87.81%);
  }
  .hasHeaderBgLeft {
    background: linear-gradient(-20deg, #fff 43.43%, #fbdef5 87.81%);
  }
}
.headerContent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.headerMenu {
  --el-menu-bg-color: var(--neutral-100);
  --el-menu-text-color: var(--neutral-900);
  border: 0;
  align-items: center;

  :deep(.el-menu-item) {
    @apply text-primary_on-brand h-12 leading-[48px] px-4 mx-3 rounded-lg font-semibold text-[18px] border-0;

    &:hover {
      @apply bg-white rounded-lg;
    }
  }
  :deep(.el-menu-item.is-active) {
    @apply bg-linear-primary !text-primary_on-brand;
    box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.05) inset;
  }
}
