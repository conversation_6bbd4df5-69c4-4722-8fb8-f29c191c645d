<template>
  <div
    class="size-full relative overflow-hidden cursor-pointer select-none"
    ref="containerRef"
    @mousemove="handleMouseMove"
  >
    <div class="absolute inset-0">
      <img
        :src="props.leftImage"
        class="size-full object-cover"
        :style="{ clipPath: `inset(0 ${100 - position}% 0 0)` }"
        alt="Before image"
      />
    </div>

    <div class="absolute inset-0">
      <img
        :src="props.rightImage"
        class="size-full object-cover"
        :style="{ clipPath: `inset(0 0 0 ${position}%)` }"
        alt="After image"
      />
    </div>

    <div
      class="absolute top-0 bottom-0 w-[2px] bg-white cursor-ew-resize"
      :style="{ left: `${position}%` }"
      @mousedown="handleMouseDown"
    >
      <div
        class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full bg-white shadow-md bg-[url('@/assets/images/home/<USER>')]"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps<{
  leftImage: string
  rightImage: string
}>()

const DEFAULT_POSITION = 50
const position = ref(DEFAULT_POSITION)
const isDragging = ref(false)
const containerRef = ref<HTMLElement>()

const handleMouseUp = () => {
  if (isDragging.value) {
    isDragging.value = false
    window.removeEventListener('mouseup', handleMouseUp)
  }
}

const handleMouseDown = () => {
  isDragging.value = true
  window.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value || !containerRef.value) return

  const rect = containerRef.value.getBoundingClientRect()
  const x = e.clientX - rect.left
  position.value = Math.min(Math.max((x / rect.width) * 100, 0), 100)
}

onMounted(() => {
  position.value = DEFAULT_POSITION
})

onUnmounted(() => {
  window.removeEventListener('mouseup', handleMouseUp)
})
</script>

<style scoped>
.cursor-ew-resize {
  cursor: ew-resize;
}

img {
  user-select: none;
  user-drag: none;
  -webkit-user-drag: none;
}
</style>
