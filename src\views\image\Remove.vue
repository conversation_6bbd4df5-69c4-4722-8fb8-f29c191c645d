<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <!-- <PictureUpload v-model:value="uploadImage" :drag="true" /> -->
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          :multiple="true"
          :limit="10"
          @onUrlChange="onUrlChangeFun"
        />
        <FormItem label="细化边缘">
          <MySlider :max="50" :min="1" v-model="sliderThin" show-input />
        </FormItem>
        <FormItem label="膨胀边缘">
          <MySlider :max="50" :min="1" v-model="sliderSwell" show-input />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { autoMatting, autoMattingList } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { toBlob } from '@/utils'
import { useImgwork } from '@/store'
const useImg = useImgwork()
const uploadImage = ref<string | null | undefined>()
const confirmLoading = ref(false)
const activeIndex = ref(0)
const artwork = useArtwork()
const userStore = useUserInfoStore()
const sliderThin = ref(6)
const sliderSwell = ref(2)
const originImg = ref()
const arr = ref([])
const onUrlChangeFun = (url: string) => {
  console.log(url, '4877')
  arr.value.push(url)
}
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()
const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  try {
    confirmLoading.value = true
    const res = await autoMattingList({
      imageUrlList: arr.value,
      detail_erode: sliderThin.value,
      detail_dilate: sliderSwell.value,
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
    arr.value = []
    // const response = await toBlob(uploadImage.value)
    // originImg.value = response
    // let formData = new FormData()
    // formData.append('image_load', originImg.value)
    // formData.append('image_url', uploadImage.value)
    // formData.append('detail_erode', String(sliderThin.value))
    // formData.append('detail_dilate', String(sliderSwell.value))
    // const res = await autoMatting(formData)
    // confirmLoading.value = false
    // userStore.updateDrawingTimes()
    // pollCreatingArtworks({
    //   ids: res?.data?.ids ?? [],
    // })
    // arr.value.forEach(async (url: string) => {
    //   const response = await toBlob(url)
    //   originImg.value = response
    //   let formData = new FormData()
    //   formData.append('image_load', originImg.value)
    //   formData.append('image_url', url)
    //   formData.append('detail_erode', String(sliderThin.value))
    //   formData.append('detail_dilate', String(sliderSwell.value))
    //   const res = await autoMatting(formData)
    //   confirmLoading.value = false
    //   userStore.updateDrawingTimes()
    //   pollCreatingArtworks({
    //     ids: res?.data?.ids ?? [],
    //   })
    // })
    // arr.value = []
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}
watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.change_head_plusFields?.imageUrl
    sliderThin.value = params?.change_head_plusFields?.detail_erode
    sliderSwell.value = params?.change_head_plusFields?.detail_dilate
  }
)
onMounted(async () => {
  uploadImage.value = useImg.imgUrlImage
})
onBeforeUnmount(() => {
  stopCreatingPoll()
  useImg.setUpdateUrlImage('')
})
</script>
<style scoped lang="scss"></style>
