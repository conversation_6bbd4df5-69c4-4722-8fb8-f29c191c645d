@font-face {
  font-family: 'iconfont';
  /* Project id 4756161 */
  src:
    url('iconfont.woff2?t=1735020853775') format('woff2'),
    url('iconfont.woff?t=1735020853775') format('woff'),
    url('iconfont.ttf?t=1735020853775') format('truetype');
}

@font-face {
  font-family: 'iconfont';
  src:
    url('iconfont1.woff2?t=1733299880208') format('woff2'),
    url('iconfont1.woff?t=1733299880208') format('woff'),
    url('iconfont1.ttf?t=1733299880208') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-text:before {
  content: '\e924';
}

.icon-extension8:before {
  content: '\e736';
}

.icon-handle:before {
  content: '\e732';
}

.icon-handleImg:before {
  content: '\e922';
}

.icon-modelFun:before {
  content: '\e921';
}

.icon-styleOrder:before {
  content: '\e60a';
}

.icon-craftRepalce:before {
  content: '\e746';
}

.icon-Text-to-Image:before {
  content: '\e929';
}

.icon-ImageGeneration:before {
  content: '\e931';
}

.icon-SpecialReplacement:before {
  content: '\e927';
}

.icon-special1:before {
  content: '\e920';
}

.icon-special2:before {
  content: '\e913';
}

.icon-special3:before {
  content: '\e915';
}

.icon-special4:before {
  content: '\e914';
}

.icon-special5:before {
  content: '\e916';
}

.icon-special6:before {
  content: '\e905';
}

.icon-extension1:before {
  content: '\e904';
}

.icon-extension2:before {
  content: '\e616';
}

.icon-extension3:before {
  content: '\e613';
}

.icon-extension4:before {
  content: '\e911';
}

.icon-extension5:before {
  content: '\e908';
}

.icon-model1:before {
  content: '\e926';
}

.icon-model2:before {
  content: '\e902';
}

.icon-model3:before {
  content: '\e906';
}

.icon-model4:before {
  content: '\e907';
}

.icon-model5:before {
  content: '\e921';
}

.icon-handel1:before {
  content: '\e930';
}

.icon-handel2:before {
  content: '\e932';
}

.icon-handel3:before {
  content: '\e928';
}

.icon-handel4:before {
  content: '\e912';
}

.icon-handel5:before {
  content: '\e909';
}

.icon-handel6:before {
  content: '\e739';
}

.icon-handel7:before {
  content: '\e917';
}

.icon-handel8:before {
  content: '\e901';
}

.icon-handel8:before {
  content: '\e901';
}

.icon-styleOrder1:before {
  content: '\e918';
}

.icon-styleOrder2:before {
  content: '\e910';
}

.icon-flower3:before {
  content: '\e734';
}

.icon-easera:before {
  content: '\e745';
}

.icon-Arrow-right:before {
  content: '\e744';
}

.icon-Warning:before {
  content: '\e743';
}

.icon-Arrow-up:before {
  content: '\e742';
}

.icon-email1:before {
  content: '\e741';
}

.icon-call:before {
  content: '\e73f';
}

.icon-delete1:before {
  content: '\e73d';
}

.icon-copy:before {
  content: '\e73e';
}

.icon-Frame:before {
  content: '\e73c';
}

.icon-import:before {
  content: '\e73b';
}

.icon-email:before {
  content: '\e60d';
}

.icon-lock:before {
  content: '\e60e';
}

.icon-pic-mine:before {
  content: '\e607';
}

.icon-logout:before {
  content: '\e609';
}

.icon-share:before {
  content: '\e605';
}

.icon-transfer:before {
  content: '\e73a';
}

.icon-paste:before {
  content: '\e739';
}

.icon-flowerPattern:before {
  content: '\e737';
}

.icon-home:before {
  content: '\e738';
}

.icon-bulb:before {
  content: '\e736';
}

.icon-text2image:before {
  content: '\e735';
}

.icon-change:before {
  content: '\e734';
}

.icon-tupianchuli:before {
  content: '\e733';
}

.icon-image2image:before {
  content: '\e732';
}

.icon-brush:before {
  content: '\e618';
}

.icon-flower:before {
  content: '\e601';
}

.icon-delete:before {
  content: '\e602';
}

.icon-undo:before {
  content: '\e603';
}

.icon-download:before {
  content: '\e604';
}

.icon-close:before {
  content: '\e606';
}

.icon-model:before {
  content: '\e608';
}

.icon-style:before {
  content: '\e60a';
}

.icon-fabric:before {
  content: '\e60b';
}

.icon-move:before {
  content: '\e60c';
}

.icon-redo:before {
  content: '\e610';
}

.icon-layer:before {
  content: '\e611';
}

.icon-extension:before {
  content: '\e613';
}

.icon-contact:before {
  content: '\e614';
}

.icon-square:before {
  content: '\e616';
}

.icon-course:before {
  content: '\e617';
}