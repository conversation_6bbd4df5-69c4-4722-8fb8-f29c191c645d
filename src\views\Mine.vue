<script setup lang="ts">
import { useAuth } from '@/hooks/useAuth'
import { useHistoryList } from '@/hooks/useHistoryList'
import { useUpdateHistoryList } from '@/hooks/useUpdateHistoryList'
import { merge } from 'lodash'
import { computed, onMounted, ref, watch, reactive } from 'vue'
import { PAGE_SIZE } from '@/config'
import { getDrawTaskUnitDetailById } from '@/clients/api/zuohua'

const visible = ref(false)
const { isLogin } = useAuth()

const filterForm = reactive<{
  dateRange?: [any, any]
  type?: number
}>({})
const pageNumRef = ref(1)

const { data, fetchData, total, loading } = useHistoryList()

const currentPicture = ref<API.TaskUnitDetailVO | null>()

// 轮询，更新进度
const progressCache = useUpdateHistoryList({
  list: data,
})

const newData = computed(() => {
  return data?.value?.map(item => {
    if (progressCache.value[item.id!]) {
      return merge({}, item, progressCache.value[item.id!])
    }
    return item
  })
})

onMounted(() => {
  if (!isLogin) {
    return
  }
  fetchData({ page: 0 })
})

watch(filterForm, () => {
  const startCreateTimeDate = filterForm.dateRange?.[0]
  const endCreateTimeDate = filterForm.dateRange?.[1]
  const params: API.TaskUnitPageRequest = {
    page: 0,
  }
  if (filterForm.type) {
    params.drawType = filterForm.type
  }
  if (startCreateTimeDate) {
    params.startCreateTime = startCreateTimeDate.getTime()
    // 返回的时间是当天的00:00:00
    params.endCreateTime = endCreateTimeDate.getTime() + 24 * 3600 * 1000
  }
  fetchData(params)
})

const handleCurrentChange = (page: number) => {
  const startCreateTimeDate = filterForm.dateRange?.[0]
  const endCreateTimeDate = filterForm.dateRange?.[1]
  const params: API.TaskUnitPageRequest = {
    page: page - 1,
  }
  if (filterForm.type) {
    params.drawType = filterForm.type
  }
  if (startCreateTimeDate) {
    params.startCreateTime = startCreateTimeDate.getTime()
    params.endCreateTime = endCreateTimeDate.getTime()
  }
  fetchData(params)
}

const getDetail = async (id: number) => {
  try {
    const res = await getDrawTaskUnitDetailById({
      drawTaskUnitId: id,
    })
    currentPicture.value = res?.data
    visible.value = true
  } catch (error) {}
}

/** 删除之后更新列表 */
const onDelete = () => {
  data.value = data.value.filter(it => it.id !== currentPicture.value?.id)
  total.value = total.value - 1
}
</script>

<template>
  <div class="wrap">
    <PageHeader :filterForm="filterForm" />
    <div
      class="flex flex-wrap items-center overflow-auto px-4"
      v-if="newData.length > 0"
    >
      <div
        v-for="image in newData"
        :key="image.id"
        class="w-[10%] aspect-square p-2 flex-shrink-0 cursor-pointer"
      >
        <MyImage
          v-if="image.status === 'SUCCESS' && !!image.result?.url"
          :src="image.result?.thumbUrl ?? image.result?.url"
          tag="v800"
          class="w-full h-full"
          imgClassName="w-full h-full object-cover rounded-lg"
          @click="() => getDetail(image.id!)"
        />
        <div
          class="flex items-center justify-center w-full h-full px-2 bg-white rounded-lg"
          v-else-if="image.status === 'GENERATING'"
        >
          <el-progress
            :percentage="(image?.completePercent ?? 0) * 100"
            :stroke-width="4"
            :show-text="false"
            color="var(--primary)"
            class="w-full"
          />
        </div>
        <div
          v-else
          class="flex items-center justify-center w-full h-full bg-white rounded-lg"
          @click="() => getDetail(image.id!)"
        >
          <MyIcon icon="Warning" :size="20" class="text-neutral-400" />
        </div>
      </div>
    </div>
    <div v-else-if="loading"></div>
    <div class="flex flex-col items-center justify-center" v-else>
      <MyImage
        class="w-[480px] mb-6"
        src="/images/pattern/empty.png"
        :lazy="false"
      />
      <div
        class="text-base text-center text-secondary whitespace-pre-wrap font-normal"
      >
        暂无历史记录
      </div>
    </div>

    <div
      class="flex justify-between items-center px-6 pt-4"
      v-if="!!newData.length || pageNumRef > 0"
    >
      <div class="text-secondary text-sm">共 {{ total }} 项数据</div>
      <el-pagination
        layout="prev, pager, next"
        :total="total"
        :page-size="PAGE_SIZE"
        v-model:current-page="pageNumRef"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <ImageDetailModal
    v-model:visible="visible"
    v-model:detail="currentPicture"
    @delete="onDelete"
  />
</template>

<style scoped lang="scss">
.wrap {
  @apply bg-neutral-100;
  min-height: calc(100vh - 80px);
}
</style>
