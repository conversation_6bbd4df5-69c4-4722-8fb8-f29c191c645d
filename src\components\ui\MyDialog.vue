<template>
  <div
    class="my-dialog"
    :class="{
      'my-dialog-bg': needFillBackground,
      'my-dialog-header-hidden': !showHeader,
    }"
  >
    <el-dialog
      v-model="dialogVisible"
      :show-close="false"
      v-bind="$attrs"
      @close="handleClose"
      :destroy-on-close="destroyOnClose"
    >
      <template #header="{ close, titleId }" v-if="showHeader">
        <div class="flex justify-between items-center">
          <div
            :id="titleId"
            class="text-[28px] font-semibold leading-[40px] text-[#23262F]"
          >
            {{ title }}
          </div>
          <div
            class="border-2 border-neutral-300 rounded-full w-10 h-10 flex justify-center items-center ml-6 cursor-pointer"
            @click="close"
          >
            <el-icon class="w-5 h-5"><CloseBold /></el-icon>
          </div>
        </div>
      </template>
      <slot></slot>
      <template #footer v-if="showFooter">
        <div class="dialog-footer flex justify-end gap-8">
          <MyButton v-if="showCancelButton" @click="handleClose">
            {{ cancelText }}
          </MyButton>
          <MyButton type="primary" @click="handleOk"> {{ okText }} </MyButton>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface DialogProps {
  okText?: string
  cancelText?: string
  visible: boolean
  title?: string
  showCancelButton?: boolean
  /** dialog是否需要背景填充 */
  needFillBackground?: boolean
  showFooter?: boolean
  showHeader?: boolean
  destroyOnClose?: boolean
}

const {
  needFillBackground,
  visible,
  okText = '确认',
  cancelText = '取消',
  showCancelButton = true,
  showFooter = true,
  showHeader = true,
  destroyOnClose = false,
} = defineProps<DialogProps>()

console.log(showHeader, 'showHeader')

const emits = defineEmits(['update:visible', 'close', 'confirm'])

const handleClose = () => {
  emits('close')
  emits('update:visible', false)
}

const handleOk = () => {
  emits('confirm')
}

const dialogVisible = computed({
  get: () => {
    return visible
  },
  set: val => emits('update:visible', val),
})
</script>

<style scoped lang="scss">
.my-dialog {
  :deep(.el-dialog) {
    padding: 32px;
    border-radius: 20px;
  }
  :deep(.el-dialog__header) {
    padding-bottom: 24px;
  }
  :deep(.el-dialog__footer) {
    padding-top: 24px;
  }
}
.my-dialog-header-hidden {
  :deep(.el-dialog) {
    margin: 0 auto;
  }
  :deep(.el-dialog__header) {
    display: none;
  }
}
.my-dialog-bg {
  :deep(.el-dialog) {
    background: linear-gradient(20deg, #fff 43.43%, #fbdef5 87.81%);
  }
}
</style>
