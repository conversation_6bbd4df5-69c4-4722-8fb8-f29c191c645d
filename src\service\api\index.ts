// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as base from './base'
import * as admin from './admin'
import * as callbackController from './callbackController'
import * as smsEndpoint from './smsEndpoint'
import * as taskCreateController from './taskCreateController'
import * as taskCreateEndpoint from './taskCreateEndpoint'
import * as serverController from './serverController'
import * as authentication from './authentication'
import * as account from './account'
import * as resource from './resource'
import * as zuohua from './zuohua'
import * as drawSimpleController from './drawSimpleController'
import * as taskModelEndpoint from './taskModelEndpoint'
export default {
  base,
  admin,
  callbackController,
  smsEndpoint,
  taskCreateController,
  taskCreateEndpoint,
  serverController,
  authentication,
  account,
  resource,
  zuohua,
  drawSimpleController,
  taskModelEndpoint,
}
