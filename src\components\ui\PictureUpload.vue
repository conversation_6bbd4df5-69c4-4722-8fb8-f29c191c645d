<template>
  <div class="my-upload">
    <el-upload
      v-bind="restProps"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :auto-upload="true"
      :http-request="customUpload"
      accept="image/*"
    >
      <div class="relative w-full h-[188px]" v-if="!showText">
        <template v-if="loading">
          <div class="w-full h-full px-8 flex justify-center items-center">
            <div class="w-full h-2.5">
              <el-progress
                :percentage="progress"
                :stroke-width="10"
                :show-text="false"
                color="var(--primary)"
              />
            </div>
          </div>
        </template>
        <template v-else-if="value">
          <div class="flex justify-center items-center w-full h-full">
            <div class="flex justify-center items-center w-full h-full">
              <!-- 有50M需求，七牛压缩参数失效，不压缩 -->
              <img
                :src="value"
                class="max-w-full max-h-full"
                v-hover-outside="onClickOutside"
                ref="buttonRef"
              />
            </div>
            <div class="absolute right-4 bottom-4">
              <MyButton type="primary" size="sm"
                >替换{{ label || '图片' }}</MyButton
              >
            </div>
          </div>
          <el-popover
            ref="popoverRef"
            :virtual-ref="buttonRef"
            trigger="hover"
            virtual-triggering
            :width="400"
            popper-class="custom-popper1"
            placement="bottom"
          >
            <div class="flex justify-center items-center w-full h-full">
              <img :src="value" class="max-w-full max-h-full" />
            </div>
          </el-popover>
        </template>

        <template v-else>
          <div class="px-4 pt-8">
            <MyImage
              :lazy="false"
              src="/images/image.png"
              class="w-14 h-14 mx-auto mb-2"
            />
            <div class="text-sm font-semibold text-tertiary">
              点击上传/拖拽图片
            </div>
            <div class="text-xs font-normal text-tertiary">
              支持图片格式：JPG、PNG
            </div>
          </div>
          <div class="absolute right-4 bottom-4">
            <MyButton type="primary" size="sm" :rounded="false">
              上传{{ label || '图片' }}
            </MyButton>
          </div>
        </template>
      </div>
      <div
        class="relative w-full h-[130px] flex flex-col justify-center"
        v-else
      >
        <template v-if="loading">
          <div class="w-full h-full px-8 flex justify-center items-center">
            <div class="w-full h-2.5">
              <el-progress
                :percentage="progress"
                :stroke-width="10"
                :show-text="false"
                color="var(--primary)"
              />
            </div>
          </div>
        </template>
        <template v-else-if="value">
          <div class="flex justify-center items-center w-full h-full">
            <!-- 有50M需求，七牛压缩参数失效，不压缩 -->
            <img :src="value" class="max-w-full max-h-full" />
            <div class="absolute right-2 top-1">
              <MyIcon
                @click="deleteImg"
                icon="close"
                :size="16"
                class="cursor-pointer text-tertiary ml-2 transition-all hover:text-brand"
              />
            </div>
          </div>
        </template>
        <template v-else>
          <div
            style="display: flex; flex-direction: column; align-items: center"
          >
            <div class="flex justify-center items-center w-full h-full">
              <MyImage :lazy="false" src="/images/image1.png" />
            </div>

            <div class="text-sm font-semibold text-tertiary">
              点击上传/拖拽图片
            </div>
            <div class="text-xs font-normal text-tertiary">
              支持图片格式：JPG、PNG
            </div>
          </div>
        </template>
      </div>
    </el-upload>
    <!-- <div
      style="position: absolute; width: 400px; height: 400px; background: pink"
    >
      111
    </div> -->
  </div>
</template>

<script lang="ts" setup>
import { useUpload } from '@/hooks/useUpload'
import { UploadProps } from 'element-plus'
import { ref, unref } from 'vue'

interface MyUploadProps extends UploadProps {
  value?: string | null
  fileType?: string[]
  sizeLimit?: number
  label?: string
  [key: string]: any
  showText: boolean
}
const buttonRef = ref()
const popoverRef = ref()
const onClickOutside = () => {
  unref(popoverRef).popperRef?.delayHide?.()
}
const { value, sizeLimit, fileType, label, showText, ...restProps } =
  defineProps<MyUploadProps>()
const handleClick = () => {
  console.log('55775')
}
const emits = defineEmits(['update:value', 'onUrlChange'])
const deleteImg = (e: { stopPropagation: () => void }) => {
  e.stopPropagation()
  emits('update:value', null)
}
const onUrlChange = (url: string, file: any) => {
  emits('update:value', url)
  emits('onUrlChange', url)
  emits('callBack', file)
}

const { beforeUpload, customUpload, progress, loading } = useUpload({
  sizeLimit: sizeLimit,
  onUrlChange: onUrlChange,
  fileType: fileType,
})
</script>
<style scoped>
.custom-popper1 {
  /* background-color: #737373 !important; */
  /* color: #fff !important; */
  height: 530px;
}
.custom-popper1 img {
  height: 530px;
  object-fit: contain;
}
</style>
<style scoped lang="scss">
.my-upload {
  :deep(.el-upload) {
    --el-upload-dragger-padding-horizontal: 0;
    --el-upload-dragger-padding-vertical: 0;
    .el-upload-dragger {
      @apply bg-transparent rounded-xl border border-solid border-tertiary;
    }
  }
}
</style>
