export interface MenuType {
  label: string
  icon?: string
  description?: string
  pathname: string
  drawType?: number
  processImage?: string
}

export const menus: MenuType[] = [
  {
    label: '首页',
    icon: 'home',
    pathname: '/',
  },
  {
    label: '创意生图',
    icon: 'text',
    pathname: '/flux',
  },
  // {
  //   label: '图生图',
  //   icon: 'handle',
  //   pathname: '/fluxImg',
  // },
  {
    label: '工艺模块',
    icon: 'craftRepalce',
    pathname: '/craft',
  },
  {
    label: '花型模块',
    icon: 'flower',
    pathname: '/flower',
  },
  {
    label: '创意延伸',
    icon: 'extension8',
    pathname: '/extension',
  },
  {
    label: '风格定制',
    icon: 'styleOrder',
    pathname: '/styleOrder',
  },
  // {
  //   label: '虚拟试衣',
  //   icon: 'handle',
  //   pathname: '/virtual',
  // },
  {
    label: '模特功能',
    icon: 'modelFun',
    pathname: '/model',
  },
  {
    label: '图片处理',
    icon: 'handleImg',
    pathname: '/image',
  },
  // {
  //   label: '面料',
  //   icon: 'handleImg',
  //   pathname: '/fabric',
  // },
]
export const subMenuMap: Record<string, MenuType[]> = {
  //文生图
  '/flux': [
    {
      label: '文生图',
      icon: 'Text-to-Image',
      pathname: '/flux',
      drawType: 20,
      description: '通过提示词描述生成出接近用户需要的款式图',
      processImage: '/images/pattern/text.png',
    },
    {
      label: '图生图',
      icon: 'ImageGeneration',
      pathname: '/flux/Img',
      drawType: 21,
      description: '通过风格接近的款式图生成出一张接近用户需要的款式图',
      processImage: '/images/pattern/img.png',
    },
  ],
  //图生图
  // '/fluxImg': [
  //   {
  //     label: '图生图',
  //     icon: 'ImageGeneration',
  //     pathname: '/fluxImg',
  //     drawType: 9651237811,
  //     description: '通过风格接近的款式图生成出一张接近用户需要的款式图',
  //     processImage: '/images/pattern/img.png',
  //   },
  // ],
  //特殊工艺替换
  '/craft': [
    {
      label: '特殊工艺替换',
      icon: 'SpecialReplacement',
      pathname: '/craft',
      drawType: 18,
      description: '根据提供的款式图片，不改变款式的前提下，只改变工艺',
      processImage: '/images/pattern/craft.png',
    },

    {
      label: '特殊工艺延伸',
      icon: 'special1',
      pathname: '/craft/craftExtension',
      drawType: 24,
      description: '特殊工艺款型延伸和扩展',
      processImage: '/images/pattern/craftExtension.png',
    },
    {
      label: '以款换面料',
      icon: 'special4',
      pathname: '/craft/fabricReplace',
      processImage: '/images/pattern/fabric.png',
      description: '同一款式替换不同的面料',
      drawType: 19,
    },
    {
      label: '面料压褶工艺',
      icon: 'special2',
      pathname: '',
    },
    {
      label: '以面料换款',
      icon: 'special3',
      pathname: '',
    },

    {
      label: '拼接面料',
      icon: 'special5',
      pathname: '',
    },
    {
      label: '面料提取',
      icon: 'special6',
      pathname: '',
    },
  ],
  // 花型模块
  '/flower': [
    {
      label: '图层分离',
      icon: 'layer',
      pathname: '/flower/layer',
      drawType: 4,
      description: '把完整的花型或面料图案分解成多个可编辑的图层',
      processImage: '/images/pattern/flower-layer.png',
    },
    {
      label: '花型提取',
      icon: 'flowerPattern',
      pathname: '',
      drawType: 7892555,
    },
    // {
    //   label: '花型提取',
    //   icon: 'flowerPattern',
    //   pathname: '/flower/extract',
    //   drawType: 5,
    //   description: '生成出可编辑的花稿矢量图',
    //   processImage: '/images/pattern/flower-extract.png',
    // },
    {
      label: '面料花型创作',
      icon: 'flower3',
      pathname: '',
    },
  ],
  //创意延伸
  '/extension': [
    {
      label: '局部重绘',
      icon: 'handel3',
      pathname: '/extension',
      drawType: 17,

      description: '针对需要更改的细节手绘图，快速更改局部部件',
      processImage: '/images/pattern/redraw.png',
    },
    {
      label: '灵感融合',
      icon: 'styleOrder2',
      pathname: '/extension/style',
      drawType: 23,
      description: '在款式不变的情况下，能够提取或融合灵感图中的元素生成为新的创意图',
      processImage: '/images/pattern/style.png',
    },
    {
      label: '图案延伸',
      icon: 'extension1',
      pathname: '',
      // pathname: '/extension',
      drawType: 96512379,
      description: '根据提供的图案，在保留原有图案文字意思和字形的基础上，做延伸设计',
      processImage: '/images/pattern/series.png',
    },

    {
      label: '四方连续',
      icon: 'extension2',
      pathname: '',
    },
    {
      label: '款式延伸',
      icon: 'extension3',
      pathname: '',
    },
    {
      label: '系列配色',
      icon: 'extension4',
      pathname: '',
    },
    {
      label: '趋势图',
      icon: 'extension5',
      pathname: '',
    },
  ],
  //图片处理
  '/image': [
    {
      label: '智能抠图',
      icon: 'handel1',
      pathname: '/image',
      drawType: 22,
      description: '轻松抠出完美边缘，让创意无拘无束！',
      processImage: '/images/pattern/remove.png',
    },
    {
      label: '分辨率提升',
      icon: 'handel2',
      pathname: '/image/resup',
      drawType: 12,
      description: '模糊变清晰，细节更惊艳，分辨率提升，让画质超越期待！',
      processImage: '/images/pattern/resup.png',
    },
    // {
    //   label: '局部重绘',
    //   icon: 'handel3',
    //   pathname: '/image/redraw',
    //   drawType: 96512378,
    //   description: '针对需要更改的细节手绘图，快速更改局部部件',
    //   processImage: '/images/pattern/redraw.png',
    // },
    {
      label: '局部替换',
      icon: 'handel4',
      pathname: '',
    },
    {
      label: '局部消除',
      icon: 'handel5',
      pathname: '',
    },
    {
      label: '图案粘贴',
      icon: 'handel6',
      pathname: '',
    },
    {
      label: '背景替换',
      icon: 'handel7',
      pathname: '',
    },
    {
      label: '细节换色',
      icon: 'handel8',
      pathname: '',
    },
  ],
  //虚拟试衣
  '/model': [
    {
      label: '模特换头',
      icon: 'model1',
      pathname: '/model',
      drawType: 13,
      description: '一键换头，百变风格，模特形象随你而定',
      processImage: '/images/pattern/changeHead.png',
    },

    {
      label: '模特换装',
      icon: 'model2',
      pathname: '/model/swap',
      drawType: 14,
      description: '将不同的服装、配饰等物品穿搭在模特身上',
      processImage: '/images/pattern/virtual.png',
    },
    {
      label: '款式图成衣',
      icon: 'model3',
      drawType: 15,
      pathname: '/model/line',
      description: '根据款式图或时装画一键生成出真实感的成衣图，且可以对成衣图的细节、面料、颜色等属性进行精准定义',
      processImage: '/images/pattern/line.png',
    },
    {
      label: '时装画成衣',
      icon: 'model4',
      pathname: '/model/clothes',
      drawType: 16,
      description: '款式图、有色线稿线稿直接生成真人上身图，能选择面料、颜色等',
      processImage: '/images/pattern/clothes.png',
    },
    {
      label: '模特定制',
      icon: 'model5',
      pathname: '',

    },
  ],
  //面料替换
  '/fabric': [
    {
      label: '面料替换',
      icon: 'ModelSwap',
      pathname: '/fabric',
      drawType: 9651237813,
      description: '把完整的花型或面料图案分解成多个可编辑的图层',
      processImage: '/images/pattern/flower-layer.png',
    },
  ],
  //虚拟试衣
  // '/virtual': [
  //   {
  //     label: '虚拟试衣',
  //     icon: 'ModelSwap',
  //     pathname: '/virtual',
  //     drawType: 9651237813,
  //     description: '把完整的花型或面料图案分解成多个可编辑的图层',
  //     processImage: '/images/pattern/flower-layer.png',
  //   },
  // ],

  '/styleOrder': [
    {
      label: '风格选择',
      icon: 'styleOrder1',
      pathname: '/styleOrder',
      drawType: 11,
      description: '可以自由选择客户和生成的款式，即可生成想要的风格款式',
      processImage: '/images/pattern/select.png',
    },
    {
      label: '风格替换',
      icon: 'styleOrder2',
      pathname: '',
    },
  ],
}
