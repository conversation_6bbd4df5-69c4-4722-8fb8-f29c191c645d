<script setup lang="ts">
import MyDialog from '@/components/ui/MyDialog.vue'
import { computed, ref } from 'vue'
import LoginEmail from './LoginEmail.vue'
import LoginPassword from './LoginPassword.vue'
import LoginCode from './LoginCode.vue'
import { useUserInfoStore } from '@/store'

const { visible } = defineProps<{ visible: boolean }>()
const emits = defineEmits(['cancel'])

const userInfo = useUserInfoStore()
const LOGIN_TYPE = {
  code: '登录',
  password: '账号密码登录',
  email: '邮箱验证码登录',
}
type ILoginType = keyof typeof LOGIN_TYPE
const loginType = ref<ILoginType>('code')
const loginTitle = computed(() => {
  return LOGIN_TYPE[loginType.value]
})
const onTypeChange = (type: ILoginType) => {
  loginType.value = type
}

const onShowSetPwd = () => {
  userInfo.setSettingLoginType('password')
}

const onClose = () => {
  userInfo.setLoginType('')
}
const onSuccess = () => {
  window.location.reload()
}
</script>
<template>
  <MyDialog
    :title="loginTitle"
    needFillBackground
    :visible="visible"
    :width="480"
    :showFooter="false"
    @close="onClose"
    :close-on-click-modal="false"
  >
    <LoginCode v-if="loginType === 'code'" @success="onSuccess" />
    <LoginPassword
      v-else-if="loginType === 'password'"
      @success="onSuccess"
      @forgotPwd="onShowSetPwd"
    />
    <LoginEmail v-else @success="onSuccess" />

    <div class="pt-8 flex justify-center gap-14 text-neutral-700 font-semibold">
      <a
        href="javascript:void(0);"
        @click="onTypeChange('code')"
        v-if="loginType !== 'code'"
        >手机验证码登录</a
      >
      <a
        href="javascript:void(0);"
        @click="onTypeChange('email')"
        v-if="loginType !== 'email'"
        >邮箱验证码登录</a
      >
      <a
        href="javascript:void(0);"
        @click="onTypeChange('password')"
        v-if="loginType !== 'password'"
        >账号密码登录</a
      >
    </div>
  </MyDialog>
</template>
