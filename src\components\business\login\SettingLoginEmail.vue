<script setup lang="ts">
import { reactive, getCurrentInstance, ref, onBeforeMount } from 'vue'
import { FormRules, FormInstance, ElMessage } from 'element-plus'
import { isMobileReg, isEmailReg, showMobile, showGlobalError } from '@/utils'
import Captcha, { type ValidInfoProps } from './Captcha.vue'
import {
  authSendVerificationCode,
  authResetEmail,
} from '@/clients/api/authentication'
import MyButton from '@/components/ui/MyButton.vue'
import MyInput from '@/components/ui/MyInput.vue'
import { useUserInfoStore } from '@/store'

const emits = defineEmits(['success', 'setTitle'])
// 当前组件实例 -- 获取form的ref
const instance = getCurrentInstance()
// store
const userStore = useUserInfoStore()

const showMobileRef = ref(showMobile(userStore.base.phone))
const form = reactive({
  mobile: userStore.base.phone,
  verificationCode: '',
  email: '',
})
const isEditEmail = ref(false)
const uploading = ref(false)

onBeforeMount(() => {
  isEditEmail.value = !userStore.base.email
})

const validateMobile = (_: any, value: any, callback: any) => {
  if (!isMobileReg.test(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}
const validateEmail = (_: any, value: any, callback: any) => {
  if (!isEmailReg.test(value)) {
    callback(new Error('请输入正确的邮箱号'))
  } else {
    callback()
  }
}
const rules = reactive<FormRules<typeof form>>({
  mobile: [
    { required: true, message: '请输入手机号' },
    { validator: validateMobile, trigger: 'blur' },
  ],
  verificationCode: [{ required: true, message: '请输入验证码' }],
  email: [
    { required: true, message: '请输入邮箱号' },
    { validator: validateEmail, trigger: 'blur' },
  ],
})

const onCaptchaSuccess = (validInfo: ValidInfoProps) => {
  // success 之后发送验证码
  authSendVerificationCode({
    type: 'PHONE',
    contact: form.mobile,
    source: 'EMAIL_RESET',
    behaviorVerifyInfo: {
      token: validInfo.token,
      authCode: validInfo.authenticate,
    },
  }).then(resp => {
    if (resp?.data) {
      ElMessage.success('验证码已发送！')
    }
  })
}

const beforeStart = async (next: () => void) => {
  const formRef = instance?.refs?.formRef as FormInstance
  await formRef.validateField('mobile')
  next()
}

const onSubmit = async () => {
  if (uploading.value) {
    return
  }
  const formRef = instance?.refs?.formRef as FormInstance
  if (!formRef) return
  const valid = await formRef.validate()
  if (!valid) {
    return
  }
  uploading.value = true
  try {
    const resp = await authResetEmail({
      phone: form.mobile,
      verificationCode: form.verificationCode,
      email: form.email,
    })
    if (resp?.data) {
      ElMessage.success('邮箱设置成功')
      emits('success')
    }
  } catch (e) {
    showGlobalError(e, '出错了，请稍后重试')
  }
  uploading.value = false
}
const onChangeEmail = () => {
  isEditEmail.value = true
  emits('setTitle', '更换邮箱')
}
</script>
<template>
  <div v-if="isEditEmail">
    <el-form
      ref="formRef"
      label-position="top"
      :model="form"
      :rules="rules"
      class="login-form"
    >
      <el-form-item label="手机号" prop="mobile">
        <my-input
          placeholder="请输入手机号"
          v-model="form.mobile"
          :maxlength="11"
          autocomplete="on"
          v-if="!showMobileRef"
        />
        <my-input :model-value="showMobileRef" disabled v-else />
      </el-form-item>
      <el-form-item label="验证码" prop="verificationCode">
        <my-input placeholder="请输入验证码" v-model="form.verificationCode">
          <template #suffix>
            <Captcha :beforeStart="beforeStart" @success="onCaptchaSuccess" />
          </template>
        </my-input>
      </el-form-item>

      <el-form-item
        :label="userStore.base.email ? '新邮箱' : '邮箱号'"
        prop="email"
      >
        <my-input
          placeholder="请输入邮箱号"
          v-model="form.email"
          autocomplete="on"
        />
      </el-form-item>
    </el-form>
    <div>
      <my-button
        type="primary"
        class="w-full"
        @click="onSubmit"
        :loading="uploading"
        >确定</my-button
      >
    </div>
  </div>
  <div v-else>
    <el-form label-position="top" class="login-form">
      <el-form-item label="邮箱号">
        <my-input
          placeholder="请输入邮箱号"
          :value="userStore.base.email"
          disabled
        />
      </el-form-item>
    </el-form>
    <div class="flex justify-center gap-14 text-neutral-700 font-semibold">
      <a @click="onChangeEmail">更换邮箱</a>
    </div>
  </div>
</template>
