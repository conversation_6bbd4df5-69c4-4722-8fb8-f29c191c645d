import { useArtwork } from '@/store'
import { useCreatingPoll } from './useCreatingPoll'
import { getDrawTaskUnitDetailById } from '@/clients/api/zuohua'

export function useClickPreviewArtwork() {
  const { stopCreatingPoll, pollCreatingArtworks } = useCreatingPoll()
  const artwork = useArtwork()

  const handleSelectPic = async (item: API.TaskUnitVO) => {
    if (!item) return
    const detail = await getDrawTaskUnitDetailById({
      drawTaskUnitId: item.id!,
    })
    console.log(detail.data, '6698')
    artwork.setDrawParams(detail?.data as API.TaskUnitDetailVO)
    stopCreatingPoll()
    if (['GENERATING'].includes(item.status!)) {
      pollCreatingArtworks({
        ids: [item.id!],
        isUpdate: false,
      })
    }
  }

  return {
    handleSelectPic,
  }
}
