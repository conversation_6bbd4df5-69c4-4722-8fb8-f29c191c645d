<template>
  <SubPageLayout :confirm-loading="isLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <FormItem label="放大倍数">
          <MySlider :max="8" :min="1" v-model="sliderStep" show-input />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { upscaleImg } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, onMounted, ref, watch, computed } from 'vue'
import { toBlob } from '@/utils'
import { useImgwork } from '@/store'
const useImg = useImgwork()
const uploadImage = ref<string | null | undefined>()
const confirmLoading = ref(false)
const activeIndex = ref(0)
const sliderStep = ref(2)
const originImg = ref()
const {
  pollCreatingArtworks,
  stopCreatingPoll,
  loading: pollingLoading,
} = useCreatingPoll()

// 合并API调用loading和轮询loading状态
const isLoading = computed(() => confirmLoading.value || pollingLoading.value)

const artwork = useArtwork()
const userStore = useUserInfoStore()
const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  try {
    confirmLoading.value = true
    const response = await toBlob(uploadImage.value)
    originImg.value = response
    let formData = new FormData()
    formData.append('image_load', originImg.value)
    formData.append('image_url', uploadImage.value)
    formData.append('scale', String(sliderStep.value))
    const res = await upscaleImg(formData)
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}
watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.upscale_img4Fields?.imageUrl
    sliderStep.value = params?.upscale_img4Fields?.scale
  }
)
onMounted(async () => {
  uploadImage.value = useImg.imgUrlResup
})
onBeforeUnmount(() => {
  stopCreatingPoll()
  useImg.setUpdateUrlResup('')
})
</script>
<style scoped lang="scss"></style>
