diff --git a/node_modules/@umijs/openapi/dist/mockGenerator.js b/node_modules/@umijs/openapi/dist/mockGenerator.js
index 3a1b817..1a3ac20 100644
--- a/node_modules/@umijs/openapi/dist/mockGenerator.js
+++ b/node_modules/@umijs/openapi/dist/mockGenerator.js
@@ -128,8 +128,10 @@ const genByTemp = ({ method, path, parameters, status, data, }) => {
             securityPath = securityPath.replace(`{${item.name}}`, `:${item.name}`);
         }
     });
-    return `'${method.toUpperCase()} ${securityPath}': (req: Request, res: Response) => {
-    res.status(${status}).send(${data});
+    return `method: '${method.toUpperCase()}', 
+    url: '${securityPath}',
+    response: (req: Request, res: Response) => {
+    return(${data});
   }`;
 };
 const genMockFiles = (mockFunction) => {
@@ -138,7 +140,7 @@ const genMockFiles = (mockFunction) => {
 import { Request, Response } from 'express';
 
 export default {
-${mockFunction.join('\n,')}
+${mockFunction}
     }`)[0];
 };
 const mockGenerator = ({ openAPI, mockFolder }) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
