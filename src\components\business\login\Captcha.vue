<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import '@/assets/yunpian.js'
import { ElMessage } from 'element-plus'

export interface ValidInfoProps {
  token: string
  authenticate: string
}
const {
  captchaId,
  beforeStart,
  value = 60,
} = defineProps<{
  captchaId?: string
  beforeStart?: (next: () => void) => void
  value?: number
}>()
const emits = defineEmits(['success'])

const isCountStart = ref(false)
const counter = ref(value)
let timer: ReturnType<typeof setInterval>
let container: HTMLDivElement
onMounted(() => {
  if (typeof YpRiddler === 'undefined') {
    return
  }
  // 在dialog、form里面样式难以覆盖
  container = document.createElement('div')
  document.body.appendChild(container)
  new YpRiddler({
    expired: 10,
    mode: 'dialog',
    noButton: true,
    winWidth: 400,
    lang: 'zh-cn',
    container: container,
    appId: '0e790ca2c37d4b3c9aaab7ac0032ea06',
    version: 'v1',
    onError: function (param: { code: number }) {
      ElMessage.closeAll()
      if (!param.code) {
        ElMessage.error('错误请求')
      } else if (Math.floor(param.code / 100) == 5) {
        // 服务不可用时，开发者可采取替代方案
        ElMessage.error('验证服务暂不可用')
      } else if (param.code == 429) {
        ElMessage.error('请求过于频繁，请稍后再试')
      } else {
        ElMessage.error('验证失败，请重试')
      }
    },
    onSuccess: function (validInfo: ValidInfoProps, close: () => void) {
      emits('success', validInfo)
      isCountStart.value = true
      close()
    },
    onFail: function (code: number, msg: string, retry: () => void) {
      retry()
    },
    beforeStart: function (next: () => void) {
      if (typeof beforeStart === 'function') {
        beforeStart(next)
      } else {
        next()
      }
    },
  })
})

watch(isCountStart, () => {
  clearInterval(timer)
  if (isCountStart) {
    timer = setInterval(() => {
      if (counter.value > 1) {
        counter.value -= 1
      } else {
        isCountStart.value = false
        counter.value = value
        clearInterval(timer)
      }
    }, 1000)
  }
})
onBeforeUnmount(() => {
  clearInterval(timer)
  !!container && document.body.removeChild(container)
})
</script>
<template>
  <div
    :id="captchaId"
    :class="[
      'cursor-pointer min-w-[72px] text-center leading-6 border-l-2 border-neutral-300 !ml-0 pl-4',
      'text-neutral-700 font-semibold',
      isCountStart && 'pointer-events-none',
    ]"
    @click="container.click"
  >
    <span v-if="isCountStart">{{ counter }}s</span>
    <span v-else>获取验证码</span>
  </div>
</template>
<style>
.yp-riddler.yp-dialog .yp-riddler-win-masker.yp-riddler-win-masker {
  z-index: 9999;
}
</style>
