// import { generateService } from '@umijs/openapi'

// const NODE_ENV = process.env.NODE_ENV || 'production'

// const endpoints = {
//   test: 'https://pref-gate.wujieai.com/v3/api-docs',
//   development: 'https://pref-gate.wujieai.com/v3/api-docs',
//   production: 'https://pref-gate.wujieai.com/v3/api-docs',
// }

// const ENDPOINT = endpoints[NODE_ENV]

// console.log(process.env.NODE_ENV, 'NODE_ENV')

// generateService({
//   schemaPath: ENDPOINT,
//   // serversPath: './src/clients',
//   requestLibPath: "import request from '../../axiosClient'",
//   enumStyle: 'enum',
//   // mockFolder: 'mock',
// })
