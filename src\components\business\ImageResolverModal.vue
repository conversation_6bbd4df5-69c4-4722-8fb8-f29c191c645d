<template>
  <MyDialog
    title="从图像解析选择输入"
    :visible="visible"
    :width="1024"
    :showFooter="true"
    @update:visible="emits('update:visible', $event)"
    @confirm="onConfirm"
  >
    <div
      class="grid grid-cols-4 gap-3 max-h-[calc(100vh-400px)] overflow-y-auto"
    >
      <div
        v-for="keyword in data"
        :key="keyword"
        class="keyword"
        :class="{ selected: selectedKeywords.includes(keyword) }"
        @click="toggleKeyword(keyword)"
      >
        {{ keyword }}
      </div>
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const { visible, data, prompt } = defineProps<{
  visible: boolean
  data: string[]
  prompt: string | null
}>()

const emits = defineEmits(['update:visible', 'confirm'])

const selectedKeywords = ref<string[]>([])

const toggleKeyword = (keyword: string) => {
  if (selectedKeywords.value.includes(keyword)) {
    selectedKeywords.value = selectedKeywords.value.filter(k => k !== keyword)
  } else {
    selectedKeywords.value.push(keyword)
  }
}

const onConfirm = () => {
  emits('confirm', selectedKeywords.value)
}

// 解析自己输入的内容，自动选中解析的tag
watch(
  () => prompt,
  () => {
    if (!prompt) {
      selectedKeywords.value = []
      return
    }
    // 自定义的一些常用分割符
    const promptKeys = prompt?.split(/[,;。，、；;]+/)
    const newSelectList = promptKeys
      ?.filter(item => !!item)
      .map(item => {
        const isExistInSelectList =
          selectedKeywords.value.findIndex(s => {
            return s?.trim() === item?.trim()
          }) > -1

        if (isExistInSelectList) {
          return selectedKeywords.value.find(s => s?.trim() === item?.trim())
        } else {
          return item?.trim()
        }
      })
    selectedKeywords.value = newSelectList as string[]
  }
)
</script>

<style scoped lang="scss">
.keyword {
  @apply bg-tertiary text-primary font-semibold text-sm rounded-full h-12 flex items-center justify-center hover:border hover:border-brand hover:cursor-pointer hover:text-brand;

  &.selected {
    @apply bg-brand text-white hover:text-white;
  }
}
</style>
