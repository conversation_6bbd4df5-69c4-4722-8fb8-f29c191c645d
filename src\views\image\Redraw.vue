<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          @onUrlChange="onUrlChangeFun"
        />
        <el-button
          @click="editImg"
          style="border-radius: 0.5rem"
          v-if="uploadImage"
          >编辑重绘区域</el-button
        >
        <FormItem label="画面描述">
          <MyTextarea
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
  <PaintModal
    v-model:visible="paintVisible"
    :bottomImage="uploadImage"
    @confirm="sureMask"
    :maskImage="maskImage || undefined"
  />
</template>
<script setup lang="ts">
import { inpatientClothes, QiNiuImgUpload } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { onBeforeUnmount, ref, watch, onMounted } from 'vue'
import { toBlob } from '@/utils'
import { useImgwork } from '@/store'
const useImg = useImgwork()
const maskImage = ref()
const prompt = ref('')
const confirmLoading = ref(false)
const paintVisible = ref(false)
const activeIndex = ref(0)
const originImg = ref()
const maskOne = ref()
const artwork = useArtwork()
const userStore = useUserInfoStore()
const route = useRoute()
const uploadImage = ref()
const router = useRouter()
const editImg = async () => {
  paintVisible.value = true
  // if (uploadImage.value.includes('api.taologyai.com')) {
  //   try {
  //     let formData = new FormData()
  //     formData.append('image_url', uploadImage.value)
  //     const res = await QiNiuImgUpload(formData)
  //     if (res.data) {
  //       uploadImage.value = res.data
  //     }
  //   } catch (error: any) {
  //     // ElMessage.error(error.message || '出错了')
  //   }
  // }
}
const count = ref(2)
const selectChange = (e: number) => {
  count.value = e
}
const sureMask = (e: { file: File; url: string }) => {
  // maskOne.value = e.file
  maskImage.value = e.url
}
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()
const onUrlChangeFun = () => {
  maskImage.value = null
  maskOne.value = null
}
const handleConfirm = async () => {
  console.log(uploadImage.value, '354')
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  if (!maskImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请涂抹图片')
    return
  }
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }
  try {
    confirmLoading.value = true
    const response = await toBlob(uploadImage.value)
    originImg.value = response
    const maskResponse = await toBlob(maskImage.value)
    maskOne.value = maskResponse
    let formData = new FormData()
    formData.append('image_origin', originImg.value)
    formData.append('image_url', String(uploadImage.value))
    formData.append('image_mask', maskOne.value)
    formData.append('image_mask_url', maskImage.value)
    formData.append('prompt', prompt.value)
    formData.append('n_iter', String(count.value))
    const res = await inpatientClothes(formData)
    confirmLoading.value = false
    activeIndex.value = 0
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

onMounted(async () => {
  uploadImage.value = useImg.imgUrl
})
watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.change_head_plusFields?.imageUrl
    maskImage.value = params?.change_head_plusFields?.imageMaskUrl
    maskOne.value = params?.change_head_plusFields?.imageMaskUrl
    prompt.value = params?.change_head_plusFields?.prompt || ''
    count.value = params?.change_head_plusFields?.n_iter
  }
)
watch(
  () => useImg.imgUrl,
  () => {
    uploadImage.value = useImg.imgUrl
    maskImage.value = null
    artwork.creatingArtworks = []
  }
)
onBeforeUnmount(() => {
  stopCreatingPoll()
  useImg.setUpdateUrl('')
})
</script>
<style scoped lang="scss">
:deep(.el-button:hover) {
  color: #606022;
  background: none;
  border-color: #dcdfe6;
}
</style>
