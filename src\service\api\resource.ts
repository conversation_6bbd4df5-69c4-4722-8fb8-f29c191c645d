// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** Get General Information Retrieves general information such as user agreement and privacy policy. GET /api/v1/resource/general_information */
export async function getGeneralInformation(options?: { [key: string]: any }) {
  return request<API.ResultGeneralInformationVO>(
    '/api/v1/resource/general_information',
    {
      method: 'GET',
      ...(options || {}),
    }
  )
}

/** Create OSS upload token Creates a new OSS upload token for the client to upload files to the OSS bucket. POST /api/v1/resource/oss_upload_token */
export async function createOssUploadToken(
  body: API.UploadTokenRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultOssUploadTokenVO>(
    '/api/v1/resource/oss_upload_token',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}
