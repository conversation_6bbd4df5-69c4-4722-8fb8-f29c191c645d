import { ref, reactive, onMounted, Ref, UnwrapRef, onBeforeMount } from 'vue'

interface ScrollConfig {
  tension?: number // 弹性系数
  duration?: number // 动画持续时间
  clamp?: boolean // 是否限制边界
}

interface ScrollState {
  scrollLeft: number
  scrollTop: number
}

interface ScrollApi {
  scrollTo: (x: number, y: number) => void
  scrollBy: (dx: number, dy: number) => void
}

export function useScroll(
  config: ScrollConfig = {}
): [UnwrapRef<ScrollState>, ScrollApi, Ref<HTMLDivElement | null>] {
  const container = ref<HTMLDivElement | null>(null)

  // 滚动状态
  const scrollState = reactive<ScrollState>({
    scrollLeft: 0,
    scrollTop: 0,
  })

  const defaultConfig: Required<ScrollConfig> = {
    tension: 300,
    duration: 500,
    clamp: true,
    ...config,
  }

  const smoothScroll = (targetLeft: number, targetTop: number) => {
    if (!container.value) return

    const startLeft = scrollState.scrollLeft
    const startTop = scrollState.scrollTop

    const distanceLeft = targetLeft - startLeft
    const distanceTop = targetTop - startTop

    const duration = defaultConfig.duration
    let startTime: number | null = null

    const step = (timestamp: number) => {
      if (!startTime) startTime = timestamp
      const progress = timestamp - startTime

      const percentage = Math.min(progress / duration, 1)
      const easeOut = 1 - Math.pow(1 - percentage, 3) // 缓动效果

      scrollState.scrollLeft = startLeft + distanceLeft * easeOut
      scrollState.scrollTop = startTop + distanceTop * easeOut

      container.value!.scrollLeft = scrollState.scrollLeft
      container.value!.scrollTop = scrollState.scrollTop

      if (percentage < 1) {
        requestAnimationFrame(step)
      }
    }

    requestAnimationFrame(step)
  }

  const scrollApi: ScrollApi = {
    scrollTo: (x, y) => {
      smoothScroll(x, y)
    },
    scrollBy: (dx, dy) => {
      smoothScroll(scrollState.scrollLeft + dx, scrollState.scrollTop + dy)
    },
  }

  // 监听滚动事件
  const onScroll = () => {
    if (!container.value) return

    scrollState.scrollLeft = container.value.scrollLeft
    scrollState.scrollTop = container.value.scrollTop
  }

  onMounted(() => {
    if (container.value) {
      container.value.addEventListener('scroll', onScroll)
    }
  })

  onBeforeMount(() => {
    if (container.value) {
      container.value.removeEventListener('scroll', onScroll)
    }
  })

  return [scrollState, scrollApi, container]
}
