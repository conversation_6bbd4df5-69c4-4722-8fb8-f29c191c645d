<script setup lang="ts">
import { reactive, getCurrentInstance, ref } from 'vue'
import { FormRules, FormInstance, ElMessage } from 'element-plus'
import { isMobileReg, setAuth, showGlobalError } from '@/utils'
import Captcha, { type ValidInfoProps } from './Captcha.vue'
import {
  authSendVerificationCode,
  authRegister,
} from '@/clients/api/authentication'
import MyButton from '@/components/ui/MyButton.vue'
import MyInput from '@/components/ui/MyInput.vue'
import Agreement from './Agreement.vue'

const emits = defineEmits(['success'])
// 当前组件实例 -- 获取form的ref
const instance = getCurrentInstance()
const form = reactive({
  mobile: '',
  verificationCode: '',
  agreement: false,
})
const uploading = ref(false)

const validateMobile = (_: any, value: any, callback: any) => {
  if (!isMobileReg.test(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}
const validateSysCode = (_: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入验证码'))
  } else {
    callback()
  }
}
const rules = reactive<FormRules<typeof form>>({
  mobile: [
    { required: true, message: '请输入手机号' },
    { validator: validateMobile, trigger: 'blur' },
  ],
  verificationCode: [
    { required: true, message: '请输入验证码' },
    { validator: validateSysCode, trigger: 'blur' },
  ],
})

const onCaptchaSuccess = (validInfo: ValidInfoProps) => {
  // success 之后发送验证码
  authSendVerificationCode({
    type: 'PHONE',
    contact: form.mobile,
    source: 'LOGIN',
    behaviorVerifyInfo: {
      token: validInfo.token,
      authCode: validInfo.authenticate,
    },
  }).then(resp => {
    if (resp?.data) {
      ElMessage.success('验证码已发送！')
    }
  })
}

const beforeStart = async (next: () => void) => {
  const formRef = instance?.refs?.formRef as FormInstance
  await formRef.validateField('mobile')
  next()
}

const onSubmit = async () => {
  if (uploading.value) {
    return
  }
  const formRef = instance?.refs?.formRef as FormInstance
  if (!formRef) return

  const valid = await formRef.validate()
  if (!valid) {
    return
  }
  if (!form.agreement) {
    return ElMessage.error('请阅读并同意服务条款')
  }
  uploading.value = true
  try {
    const resp = await authRegister({
      phone: form.mobile,
      verificationCode: form.verificationCode,
    })
    setAuth(resp?.data || '')
    emits('success')
    ElMessage.success('登录成功')
  } catch (e) {
    showGlobalError(e, '出错了，请稍后重试')
  }
  uploading.value = false
}
</script>
<template>
  <el-form
    ref="formRef"
    label-position="top"
    :model="form"
    :rules="rules"
    class="login-form"
  >
    <el-form-item label="手机号" prop="mobile">
      <my-input
        placeholder="请输入手机号"
        v-model="form.mobile"
        :maxlength="11"
        autocomplete="on"
      />
    </el-form-item>
    <el-form-item label="验证码" prop="verificationCode">
      <my-input placeholder="请输入验证码" v-model="form.verificationCode">
        <template #suffix>
          <Captcha :beforeStart="beforeStart" @success="onCaptchaSuccess" />
        </template>
      </my-input>
    </el-form-item>
  </el-form>
  <Agreement v-model="form.agreement" />
  <div>
    <my-button
      type="primary"
      class="w-full"
      @click="onSubmit"
      :loading="uploading"
      >确定</my-button
    >
  </div>
</template>
