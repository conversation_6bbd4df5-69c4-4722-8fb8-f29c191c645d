// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** 此处后端没有提供注释 POST /verifyCodeSmsSend */
export async function verifyCodeSmsSend(
  body: API.SmsSendTemplateReq,
  options?: { [key: string]: any }
) {
  return request<any>('/verifyCodeSmsSend', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}
