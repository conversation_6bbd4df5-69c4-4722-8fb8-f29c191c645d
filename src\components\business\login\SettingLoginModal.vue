<script setup lang="ts">
import MyDialog from '@/components/ui/MyDialog.vue'
import { ref, computed } from 'vue'
import SettingLoginPwd from './SettingLoginPwd.vue'
import SettingLoginEmail from './SettingLoginEmail.vue'
import { useUserInfoStore } from '@/store'

// store
const userStore = useUserInfoStore()

const LOGIN_TYPE = {
  password: '密码设置',
  email: '邮箱设置',
}

const { visible } = defineProps<{
  visible: boolean
}>()

const loginType = userStore.settingLoginType || 'password'

const loginTitle = ref(LOGIN_TYPE[loginType as keyof typeof LOGIN_TYPE])

/** functions */
const onSetTitle = (title: string) => {
  loginTitle.value = title
}
const onClose = () => {
  userStore.setSettingLoginType('')
}
const onSuccess = () => {
  onClose()
}
</script>
<template>
  <MyDialog
    :title="
      (userStore.base?.passwordInitialized && loginType !== 'email') ||
      userStore.forgotPwdPhone
        ? '重置密码'
        : loginTitle
    "
    needFillBackground
    :visible="visible"
    :width="480"
    :showFooter="false"
    @close="onClose"
    :close-on-click-modal="false"
  >
    <SettingLoginPwd v-if="loginType === 'password'" @success="onSuccess" />
    <SettingLoginEmail v-else @success="onSuccess" @setTitle="onSetTitle" />
  </MyDialog>
</template>
