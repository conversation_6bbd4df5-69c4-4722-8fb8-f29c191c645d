// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** 作画任务批量删除 POST /api/v1/draw/batch_delete_task_units_by_ids */
export async function batchDeleteTaskUnitsByIds(
  body: API.TaskUnitIdsReq,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean>(
    '/api/v1/draw/batch_delete_task_units_by_ids',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** Change Style Fabric Changes the fabric of a specific style based on the provided fabric details POST /api/v1/draw/change_fabric */
export async function changeFabric(
  body: API.CommonDrawReqFabricChangeRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultArtworkCreateResultVO>(
    '/api/v1/draw/change_fabric',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 面料花型制作 POST /api/v1/draw/fabric_pattern_creation */
export async function fabricPatternCreation(
  body: API.CommonDrawReqFabricPatternCreationFieldsVO,
  options?: { [key: string]: any }
) {
  return request<API.ResultArtworkCreateResultVO>(
    '/api/v1/draw/fabric_pattern_creation',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 作画任务详情 GET /api/v1/draw/get_draw_task_unit_detail_by_id */
export async function getDrawTaskUnitDetailById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDrawTaskUnitDetailByIdParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultTaskUnitDetailVO>(
    '/api/v1/draw/get_draw_task_unit_detail_by_id',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  )
}

/** 图层分离 图层分离，根据传入底图及分离层数，返回分离后的图层 POST /api/v1/draw/layer_separation */
export async function layerSeparation(
  body: API.CommonDrawReqLayerSeparationRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultArtworkCreateResultVO>(
    '/api/v1/draw/layer_separation',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 此处后端没有提供注释 POST /api/v1/draw/list_draw_task_unit_details_by_ids */
export async function listDrawTaskUnitDetailsByIds(
  body: API.TaskUnitIdsReq,
  options?: { [key: string]: any }
) {
  return request<API.ResultListTaskUnitDetailVO>(
    '/api/v1/draw/list_draw_task_unit_details_by_ids',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 此处后端没有提供注释 POST /api/v1/draw/list_draw_task_units_by_ids */
export async function listDrawTaskUnitsByIds(
  body: API.TaskUnitIdsReq,
  options?: { [key: string]: any }
) {
  return request<API.ResultListTaskUnitVO>(
    '/api/v1/draw/list_draw_task_units_by_ids',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 作画任务详情列表 POST /api/v1/draw/page_task_details */
export async function pageTaskDetails(
  body: API.TaskUnitPageRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageResponseTaskUnitDetailVO>(
    '/api/v1/draw/page_task_details',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 作画任务列表 POST /api/v1/draw/page_task_units */
export async function pageTaskUnits(
  body: API.TaskUnitPageRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageResponseTaskUnitVO>(
    '/api/v1/draw/page_task_units',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 印花位循环 POST /api/v1/draw/pattern_cycle */
export async function patternCycle(
  body: API.CommonDrawReqPatternCycleFieldsVO,
  options?: { [key: string]: any }
) {
  return request<API.ResultArtworkCreateResultVO>(
    '/api/v1/draw/pattern_cycle',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 图案提取 图案提取，根据传入描述词提取图案 POST /api/v1/draw/pattern_extraction */
export async function patternExtraction(
  body: API.CommonDrawReqPatternExtractionRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultArtworkCreateResultVO>(
    '/api/v1/draw/pattern_extraction',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 图案粘贴 POST /api/v1/draw/pattern_replace */
export async function patternReplace(
  body: API.CommonDrawReqPatternReplaceFieldsVO,
  options?: { [key: string]: any }
) {
  return request<API.ResultArtworkCreateResultVO>(
    '/api/v1/draw/pattern_replace',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** 图层分离预处理 图层分离预处理，返回最大图层数量 GET /api/v1/draw/pre_layer_separation */
export async function preLayerSeparation(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.preLayerSeparationParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultPreLayerSeparationResultVO>(
    '/api/v1/draw/pre_layer_separation',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  )
}

/** 预处理图案提取 预处理图案提取，根据输入图片解析图片tag GET /api/v1/draw/pre_pattern_extraction */
export async function prePatternExtraction(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.prePatternExtractionParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultPrePatternExtractionResultVO>(
    '/api/v1/draw/pre_pattern_extraction',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    }
  )
}

/** 款式延伸 POST /api/v1/draw/style_extension */
export async function styleExtension(
  body: API.CommonDrawReqStyleExtensionFieldsVO,
  options?: { [key: string]: any }
) {
  return request<API.ResultArtworkCreateResultVO>(
    '/api/v1/draw/style_extension',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}
