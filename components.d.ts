/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Agreement: typeof import('./src/components/business/login/Agreement.vue')['default']
    Autoscroll: typeof import('./src/components/ui/Autoscroll.vue')['default']
    Captcha: typeof import('./src/components/business/login/Captcha.vue')['default']
    CardScroller: typeof import('./src/components/business/home/<USER>')['default']
    Coverflow: typeof import('./src/components/business/home/<USER>')['default']
    DefaultContentArea: typeof import('./src/components/ui/DefaultContentArea.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FeatureCard: typeof import('./src/components/business/home/<USER>')['default']
    FormItem: typeof import('./src/components/ui/FormItem.vue')['default']
    GenerateCount: typeof import('./src/components/business/GenerateCount.vue')['default']
    GradientButton: typeof import('./src/components/business/home/<USER>')['default']
    Help: typeof import('./src/components/ui/Help.vue')['default']
    History: typeof import('./src/components/business/History.vue')['default']
    HistoryBar: typeof import('./src/components/business/HistoryBar.vue')['default']
    ImageDetailModal: typeof import('./src/components/business/ImageDetailModal.vue')['default']
    ImageDiff: typeof import('./src/components/business/home/<USER>')['default']
    ImageResolverModal: typeof import('./src/components/business/ImageResolverModal.vue')['default']
    ImageViewer: typeof import('./src/components/business/imageViewer/index.vue')['default']
    LoginCode: typeof import('./src/components/business/login/LoginCode.vue')['default']
    LoginEmail: typeof import('./src/components/business/login/LoginEmail.vue')['default']
    LoginModal: typeof import('./src/components/business/login/LoginModal.vue')['default']
    LoginPassword: typeof import('./src/components/business/login/LoginPassword.vue')['default']
    MenuSwitchDialog: typeof import('./src/components/business/MenuSwitchDialog.vue')['default']
    MyButton: typeof import('./src/components/ui/MyButton.vue')['default']
    MyDialog: typeof import('./src/components/ui/MyDialog.vue')['default']
    MyIcon: typeof import('./src/components/ui/MyIcon.vue')['default']
    MyImage: typeof import('./src/components/ui/MyImage.vue')['default']
    MyInput: typeof import('./src/components/ui/MyInput.vue')['default']
    MySelect: typeof import('./src/components/ui/MySelect.vue')['default']
    MySlider: typeof import('./src/components/ui/MySlider.vue')['default']
    MyTextarea: typeof import('./src/components/ui/MyTextarea.vue')['default']
    MyTextarea1: typeof import('./src/components/ui/MyTextarea1.vue')['default']
    Navigation: typeof import('./src/components/business/imageViewer/Navigation.vue')['default']
    PageHeader: typeof import('./src/components/business/mine/PageHeader.vue')['default']
    Paint: typeof import('./src/components/business/Paint.vue')['default']
    PaintModal: typeof import('./src/components/business/PaintModal.vue')['default']
    PictureUpload: typeof import('./src/components/ui/PictureUpload.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SettingLoginEmail: typeof import('./src/components/business/login/SettingLoginEmail.vue')['default']
    SettingLoginModal: typeof import('./src/components/business/login/SettingLoginModal.vue')['default']
    SettingLoginPwd: typeof import('./src/components/business/login/SettingLoginPwd.vue')['default']
    StepInput: typeof import('./src/components/ui/StepInput.vue')['default']
    SubPageLayout: typeof import('./src/components/business/SubPageLayout.vue')['default']
    Tabs: typeof import('./src/components/business/home/<USER>')['default']
    UpdateProfileModal: typeof import('./src/components/business/settings/UpdateProfileModal.vue')['default']
    ValidateAccountModal: typeof import('./src/components/business/login/ValidateAccountModal.vue')['default']
    ValidateCode: typeof import('./src/components/business/login/ValidateCode.vue')['default']
    ValidateEmail: typeof import('./src/components/business/login/ValidateEmail.vue')['default']
  }
}
