{"name": "taoandcompany-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm run gentypes && vite --mode production", "dev:test": "npm run gentypes:test && vite --host --mode test", "build": "npm run gentypes && vue-tsc --noEmit && vite build --mode production", "build:test": "npm run gentypes:test && vue-tsc --noEmit && vite build --mode test", "preview": "vite preview", "gentypes": "cross-env NODE_ENV=production ts-node openapi.config.js", "gentypes:test": "cross-env NODE_ENV=test ts-node openapi.config.js", "prepare": "husky", "lint-staged": "lint-staged", "sync:icon": "node src/scripts/genIconFont.cjs", "postinstall": "patch-package"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@super-puzzle/paint": "^1.1.3", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "element-plus": "^2.8.6", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "pinia": "^2.2.4", "qiniu-js": "3.4.1", "swiper": "^11.1.15", "ua-parser-js": "^2.0.0", "vue": "^3.5.10", "vue-router": "^4.4.5", "vue3-lazy": "^1.0.0-alpha.1"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^20.14.11", "@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "file-saver": "^2.0.5", "husky": "^9.1.6", "jszip": "^3.10.1", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "patch-package": "^6.5.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "sass-embedded": "1.79.1", "tailwindcss": "^3.4.14", "ts-node": "^10.9.2", "typescript": "^5.5.3", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.10", "vite-plugin-mock": "^3.0.2", "vue-tsc": "^2.1.6"}, "volta": {"node": "20.18.0", "yarn": "1.22.21"}}