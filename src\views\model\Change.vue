<template>
  <SubPageLayout :confirm-loading="isLoading" @confirm="handleConfirm">
    <template #nav>
      <div
        style="
          background: #fff;
          height: 2.875rem;
          border-radius: 0.75rem;
          display: flex;

          align-items: center;
          justify-content: center;
          text-align: center;
          margin-bottom: 0.625rem;
        "
      >
        <div
          style="
            width: 48%;
            cursor: pointer;
            line-height: 2.25rem;
            border-radius: 0.625rem;
          "
          @click="changeNav(0)"
        >
          <MyButton
            type="primary"
            :rounded="false"
            style="height: 2.25rem; width: 100%"
            v-show="NavNum == 0"
            >自动换头</MyButton
          >
          <span v-show="NavNum == 1">自动换头</span>
        </div>
        <div
          style="
            width: 48%;
            cursor: pointer;
            height: 2.25rem;
            line-height: 2.25rem;
            border-radius: 0.625rem;
          "
          @click="changeNav(1)"
        >
          <MyButton
            type="primary"
            :rounded="false"
            style="height: 2.25rem; width: 100%"
            v-show="NavNum == 1"
            >涂抹换头</MyButton
          >
          <span v-show="NavNum == 0">涂抹换头</span>
        </div>
      </div></template
    >
    <template #form>
      <div class="flex flex-col gap-4 h-full" v-if="NavNum == 0">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <FormItem label="性别">
          <GenerateCount
            :counts="radioList"
            :count="radioCount"
            @onChangeSelect="selectChange1"
          />
        </FormItem>
        <FormItem label="模特年龄段">
          <MySelect v-model="selectVal" :list="ageList" />
        </FormItem>
        <!-- <FormItem label="发型选择">
          <MySelect v-model="selectVal1" :list="hairList" />
        </FormItem> -->
        <FormItem label="头色选择">
          <MySelect v-model="selectVal2" :list="colorList" />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
      </div>
      <div class="flex flex-col gap-4 h-full" v-else>
        <PictureUpload
          v-model:value="uploadImage1"
          :drag="true"
          @onUrlChange="onUrlChangeFun1"
        />
        <el-button
          @click="editImg"
          style="border-radius: 0.5rem"
          v-if="uploadImage1"
          >编辑重绘区域</el-button
        >
        <FormItem label="画面描述">
          <MyTextarea
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count1" @onChangeSelect="selectChange2" />
        </FormItem>
      </div>
    </template>
    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
  <PaintModal
    v-model:visible="paintVisible"
    :bottomImage="uploadImage1"
    @confirm="sureMask"
    :maskImage="maskImage || undefined"
  />
</template>
<script setup lang="ts">
import { changeHead, changeHeadMask } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, ref, watch, onMounted, computed } from 'vue'
import { toBlob } from '@/utils'
import { useImgwork } from '@/store'
const useImg = useImgwork()
const uploadImage = ref()
const uploadImage1 = ref()
const maskImage = ref()
const confirmLoading = ref(false)
const activeIndex = ref(0)
const paintVisible = ref(false)
const artwork = useArtwork()
const userStore = useUserInfoStore()
const maskOne = ref()
const originImg = ref()
const originImg1 = ref()
const prompt = ref('')
const radioList = ref(['男', '女'])
const radioCount = ref('女')
const count = ref(2)
const count1 = ref(2)
const selectVal = ref('20-30')
// const selectVal1 = ref('大波浪')
const selectVal2 = ref('浅金色')
const NavNum = ref(0)
const changeNav = (num: number) => {
  uploadImage.value = ''
  uploadImage1.value = ''
  NavNum.value = num
  artwork.creatingArtworks = []
  prompt.value = ''
}
const editImg = () => {
  console.log(uploadImage.value, '8989S')
  paintVisible.value = true
}
const sureMask = (e: { file: File; url: string }) => {
  maskOne.value = e.file
  maskImage.value = e.url
}
const ageList = [
  { label: '0-10', value: '0-10' },
  { label: '10-20', value: '10-20' },
  { label: '20-30', value: '20-30' },
  { label: '30-40', value: '30-40' },
  { label: '40-50', value: '40-50' },
  { label: '50-60', value: '50-60' },
  { label: '60-70', value: '60-70' },
]
// const hairList = [
//   { label: '大波浪', value: '大波浪' },
//   { label: '直顺', value: '直顺' },
// ]
const colorList = [
  { label: '黑色', value: '黑色' },
  { label: '深棕色', value: '深棕色' },
  { label: '浅棕色', value: '浅棕色' },
  { label: '金色', value: '金色' },
  { label: '浅金色', value: '浅金色' },
  { label: '红色', value: '红色' },
  { label: '紫色', value: '紫色' },
  { label: '灰色', value: '灰色' },
  { label: '银色', value: '银色' },
  { label: '白色', value: '白色' },
  { label: '蓝色', value: '蓝色' },
  { label: '绿色', value: '绿色' },
  { label: '粉色', value: '粉色' },
]
const selectChange = (e: number) => {
  count.value = e
}
const selectChange2 = (e: number) => {
  count1.value = e
}
const selectChange1 = (e: string) => {
  radioCount.value = e
}
const onUrlChangeFun1 = () => {
  maskImage.value = null
  maskOne.value = null
}
const {
  pollCreatingArtworks,
  stopCreatingPoll,
  loading: pollingLoading,
} = useCreatingPoll()

// 合并API调用loading和轮询loading状态
const isLoading = computed(() => confirmLoading.value || pollingLoading.value)
const handleConfirm = async () => {
  if (NavNum.value == 0) {
    if (!uploadImage.value) {
      ElMessage.closeAll()
      ElMessage.error('请上传图片')
      return
    }
    try {
      confirmLoading.value = true
      const response = await toBlob(uploadImage.value)
      originImg.value = response
      let formData = new FormData()
      formData.append('image_load', originImg.value)
      formData.append('image_url', uploadImage.value)
      formData.append('sex', radioCount.value)
      formData.append('age', selectVal.value)
      formData.append('headache', selectVal2.value)
      formData.append('n_iter', String(count.value))
      const res = await changeHead(formData)
      confirmLoading.value = false
      activeIndex.value = 0
      userStore.updateDrawingTimes()
      pollCreatingArtworks({
        ids: res?.data?.ids ?? [],
      })
    } catch (error: any) {
      confirmLoading.value = false
      if (error == 'Unauthorized') {
        ElMessage.error('请登录后使用')
      } else {
        ElMessage.error(error.message || '出错了')
      }
    }
  } else {
    if (!uploadImage1.value) {
      ElMessage.closeAll()
      ElMessage.error('请上传图片')
      return
    }
    if (!maskImage.value) {
      ElMessage.closeAll()
      ElMessage.error('请涂抹图片')
      return
    }
    if (!prompt.value) {
      ElMessage.closeAll()
      ElMessage.error('请输入画面描述')
      return
    }
    try {
      confirmLoading.value = true
      const response = await toBlob(uploadImage1.value)
      originImg1.value = response
      const maskResponse = await toBlob(maskImage.value)
      maskOne.value = maskResponse
      let formData = new FormData()
      formData.append('image_load', originImg1.value)
      formData.append('image_url', uploadImage1.value)
      formData.append('image_mask', maskOne.value)
      formData.append('image_mask_url', maskImage.value)
      formData.append('prompt', prompt.value)
      formData.append('n_iter', String(count1.value))
      const res = await changeHeadMask(formData)
      confirmLoading.value = false
      activeIndex.value = 0
      userStore.updateDrawingTimes()
      pollCreatingArtworks({
        ids: res?.data?.ids ?? [],
      })
    } catch (error: any) {
      confirmLoading.value = false
      if (error == 'Unauthorized') {
        ElMessage.error('请登录后使用')
      } else {
        ElMessage.error(error.message || '出错了')
      }
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    if (params?.change_head_plusFields?.headType == '自动换头') {
      NavNum.value = 0
      uploadImage.value = params?.change_head_plusFields?.imageUrl
      radioCount.value = params?.change_head_plusFields?.sex
      selectVal.value = params?.change_head_plusFields?.age
      selectVal2.value = params?.change_head_plusFields?.headache
      count.value = params?.change_head_plusFields?.n_iter
    } else {
      NavNum.value = 1
      uploadImage1.value = params?.change_head_plusFields?.imageUrl
      maskImage.value = params?.change_head_plusFields?.imageMaskUrl
      prompt.value = params?.change_head_plusFields?.prompt || ''
      count1.value = params?.change_head_plusFields?.n_iter
    }
  }
)
onMounted(async () => {
  uploadImage.value = useImg.imgUrlModel
})
onBeforeUnmount(() => {
  stopCreatingPoll()
  useImg.setUpdateUrlModel('')
})
</script>
<style scoped lang="scss">
:deep(.el-button:hover) {
  color: #606022;
  background: none;
  border-color: #dcdfe6;
}
</style>
