export const PAGE_SIZE = 40

export interface SeedOption {
  key: string
  path: string
  label: string
  children?: SeedOption[]
}
export const SEED_OPTIONS: SeedOption[] = [
  {
    key: 'flower',
    path: '/flower',
    label: '花型模块',
    children: [
      {
        key: 'flower-layer-initImage',
        path: '/flower/layer',
        label: '图层分离',
      },
      {
        key: 'flower-extract-initImage',
        path: '/flower/extract',
        label: '花型提取',
      },
    ],
  },
  {
    key: 'creativity',
    path: '/creativity',
    label: '创意延伸',
    children: [
      {
        key: 'creativity-repeat-initImage',
        path: '/creativity/repeat',
        label: '四方连续',
      },
      {
        key: 'creativity-extend-initImage',
        path: '/creativity/extend',
        label: '款式延伸',
      },
      {
        key: 'creativity-flower-initImage',
        path: '/creativity/flower',
        label: '面料花型创作',
      },
    ],
  },
  {
    key: 'style',
    path: '/style',
    label: '风格定制',
    children: [
      {
        key: 'style-extension',
        path: '/style/extension',
        label: '以款换面料',
        children: [
          {
            key: 'style-extension-styleImage',
            path: '/style/extension',
            label: '款式图',
          },
          {
            key: 'style-extension-fabricImage',
            path: '/style/extension',
            label: '面料图',
          },
        ],
      },
    ],
  },
  {
    key: 'image',
    path: '/image',
    label: '图片处理',
    children: [
      {
        key: 'image-paste',
        path: '/image/paste',
        label: '图案粘贴',
        children: [
          {
            key: 'image-paste-initImage',
            path: '/image/paste',
            label: '款式图',
          },
          {
            key: 'image-paste-elementImage',
            path: '/image/paste',
            label: '元素图',
          },
        ],
      },
    ],
  },
]
