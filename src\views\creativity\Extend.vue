<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <!-- <MyButton
          class="flex-shrink-0"
          type="form"
          icon="import"
          :disabled="!uploadImage"
          @click="editVisible = true"
          >编辑重绘区域</MyButton
        > -->
        <FormItem label="画面描述（提取元素）">
          <MyTextarea
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount v-model:count="count" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>

  <!-- <PaintModal
    v-model:visible="editVisible"
    @confirm="maskImage = $event"
    :bottomImage="uploadImage || undefined"
    :maskImage="maskImage || undefined"
  /> -->
</template>
<script setup lang="ts">
import { styleExtension } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, ref, watch } from 'vue'

// const editVisible = ref(false)
const uploadImage = ref<string | null | undefined>()
// const maskImage = ref<string | null | undefined>()
const prompt = ref('')
const count = ref<number>(4)
const confirmLoading = ref(false)
const activeIndex = ref(0)

const artwork = useArtwork()
const userStore = useUserInfoStore()

const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }

  try {
    confirmLoading.value = true
    const res = await styleExtension({
      num: count.value,
      drawParam: {
        initImage: uploadImage.value,
        // maskImage: maskImage.value || undefined,
        prompt: prompt.value,
      },
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    ElMessage.error(error.message || '出错了')
  }
}

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    !!params.id && artwork.setCreatingArtworks([params])
    uploadImage.value = params?.styleExtensionFields?.initImage
    prompt.value = params?.styleExtensionFields?.prompt || ''
    // maskImage.value = params?.styleExtensionFields?.maskImage
  },
  { deep: true, immediate: true }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss"></style>
