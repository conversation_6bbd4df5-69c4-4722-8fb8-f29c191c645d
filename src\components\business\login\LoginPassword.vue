<script setup lang="ts">
import { reactive, getCurrentInstance, ref } from 'vue'
import { FormRules, FormInstance, ElMessage } from 'element-plus'
import { isMobileReg, setAuth, showGlobalError } from '@/utils'
import { authLogin } from '@/clients/api/authentication'
import MyButton from '@/components/ui/MyButton.vue'
import MyInput from '@/components/ui/MyInput.vue'
import Agreement from './Agreement.vue'
import CryptoJS from 'crypto-js'
import { useUserInfoStore } from '@/store'

const userStore = useUserInfoStore()

const emits = defineEmits(['success', 'forgotPwd'])
// 当前组件实例 -- 获取form的ref
const instance = getCurrentInstance()
const form = reactive({
  mobile: '',
  password: '',
  agreement: false,
})
const uploading = ref(false)

const validateMobile = (_: any, value: any, callback: any) => {
  if (!isMobileReg.test(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}
const validatePassword = (_: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入验证码'))
  } else {
    callback()
  }
}
const rules = reactive<FormRules<typeof form>>({
  mobile: [
    { required: true, message: '请输入手机号' },
    { validator: validateMobile, trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码' },
    { validator: validatePassword, trigger: 'blur' },
  ],
})

const onSubmit = async () => {
  if (uploading.value) {
    return
  }
  const formRef = instance?.refs?.formRef as FormInstance
  if (!formRef) return
  const valid = await formRef.validate()
  if (!valid) {
    return
  }
  if (!form.agreement) {
    return ElMessage.error('请阅读并同意服务条款')
  }
  uploading.value = true
  try {
    const resp = await authLogin({
      loginMethod: 'PASSWORD',
      username: form.mobile,
      password: CryptoJS.MD5(form.password).toString(),
    })
    setAuth(resp?.data || '')
    emits('success')
    ElMessage.success('登录成功')
  } catch (err: any) {
    console.log('err', err?.code === '********')

    if (err?.code === '********') {
      // 触发安全校验
      userStore.setValidateAccountInfo(true)
    } else {
      showGlobalError(err, '出错了，请稍后重试')
    }
  }
  uploading.value = false
}
const onForgotPwd = () => {
  if (!form.mobile || !isMobileReg.test(form.mobile)) {
    return
  }
  userStore.setForgotPwdPhone(form.mobile)
  emits('forgotPwd')
}
</script>
<template>
  <el-form
    ref="formRef"
    label-position="top"
    :model="form"
    :rules="rules"
    class="login-form"
  >
    <el-form-item label="手机号" prop="mobile">
      <my-input
        placeholder="请输入手机号"
        v-model="form.mobile"
        :maxlength="11"
        autocomplete="on"
      />
    </el-form-item>
    <el-form-item label="密码" prop="password">
      <my-input
        placeholder="请输入密码"
        v-model="form.password"
        type="password"
        maxlength="18"
      >
        <template #suffix>
          <div
            :class="[
              'cursor-pointer min-w-[72px] text-center leading-6 border-l-2 border-neutral-300 !ml-0 pl-4',
              'text-neutral-700 font-semibold',
            ]"
            @click="onForgotPwd"
          >
            <span>忘记密码</span>
          </div>
        </template>
      </my-input>
    </el-form-item>
  </el-form>
  <Agreement v-model="form.agreement" />
  <div>
    <my-button
      type="primary"
      class="w-full"
      @click="onSubmit"
      :loading="uploading"
      >确定</my-button
    >
  </div>
</template>
