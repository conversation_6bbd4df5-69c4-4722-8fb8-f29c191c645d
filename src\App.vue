<script setup lang="ts">
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { onMounted } from 'vue'
onMounted(() => {
  if (navigator.language !== 'zh-CN') {
    window.location.href = 'http://taologyai.com/en'
  }
})
</script>

<template>
  <el-config-provider :locale="zhCn">
    <router-view />
  </el-config-provider>
</template>

<style scoped lang="scss">
</style>
