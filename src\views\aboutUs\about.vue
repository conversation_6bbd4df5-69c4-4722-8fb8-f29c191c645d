<template>
  <div class="about">
    <div>为什么选择Taology AI?</div>
  </div>
  <div>
    <div
      style="
        font-size: 28px;
        font-weight: 550;
        color: #000;
        letter-spacing: 8px;
        font-family: PingFang SC;
        display: flex;
        height: 80px;
        align-items: center;
        border: 1px solid #737373;
        padding-left: 100px;
      "
    >
      关于Taology AI
    </div>
    <div class="content">
      <div>
        Taology AI
        是一款专为时尚女装设计领域量身打造的尖端智能设计工具。它借助生成式人工智能的强大力量，彻底革新了创意流程，能瞬间将设计师的构思或手绘草图转化为栩栩如生的成衣视觉效果图。这一创新极大地简化了传统设计流程中的重复性任务，显著加快了设计迭代的速度。
      </div>
      <div>
        在传统设计领域，设计师们需要掌握多种专业工具。在寻找灵感时，他们依赖 Pinterest、Instagram、Google
        Trends、 WGSN 等平台。进行平面设计时，他们使用
        Illustrator、Photoshop、InDesign 、CorelDRAW
        等软件。三维建模则借助CLO3D、Browzwear、Style3D等专业图形工具，即便完成设计后，他们仍需关注
        Vogue、BoF、Behance等30多个行业平台，以紧跟最新潮流。
      </div>
      <div>
        然而，市场上现有的 AI 设计工具存在诸多局限。像 Stable Diffusion 和
        Midjourney
        这类通用工具，对提示词设计要求极高，设计师需要投入大量时间和精力去学习。CLO3D
        和 Optitex 等行业专用软件，更侧重于技术实现，缺乏创意辅助，而面向电商的
        Canva 和 Fashion CAD
        等工具，难以满足以产销一体为主要营业模式的设计需求。
      </div>
      <div>
        为解决这些问题，Taology AI
        为时尚设计师提供了一套量身定制的智能设计系统。它集成了多种创意引擎，包括“款式延伸”“花型提取”“局部改款”“图案延伸”和“线稿成衣”等功能。本质上，Taology
        AI
        是一个一体化智能工作平台，通过AIGC技术实现手绘草图到高精度视觉效果图的实时转换。
      </div>
      <div>
        与通用设计软件不同，Taology AI
        与女装设计的整个流程深度契合。从灵感捕捉到款式拓展，再到虚拟试衣，每个步骤都能在
        Taology AI
        环境中完成。该平台真正释放了设计师的创造力，操作简便，易于上手，无论是时尚设计专业的学生、设计助理、资深设计师等专业设计人员，还是买手、业务员等非设计职业人员都能快速将自己的设计理念变为现实。
      </div>
    </div>
    <Footer />
  </div>
</template>
<script setup>
import Footer from '@/layout/Footer.vue'
</script>
<style scoped lang="scss">
.about {
  width: 100%;
  height: 745px;
  background: url('/images/issue/aboutBg.png') no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 58px;
  font-weight: 700;
  color: #fff;
  letter-spacing: 8px;
  font-family: Alibaba Sans Thai;
}
.content {
  padding: 0 100px;
  font-size: 16px;
  color: #737373;
  font-family: PingFang SC;
  //   font-weight: 500;
  letter-spacing: 5px;
  margin-bottom: 60px;
  > div {
    margin-top: 20px;
  }
}
</style>