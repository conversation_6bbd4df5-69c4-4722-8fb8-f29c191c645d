import { listDrawTaskUnitDetailsByIds } from '@/clients/api/zuohua'
import { cloneDeep } from 'lodash'
import { onBeforeUnmount, Ref, ref, watch } from 'vue'

export function useUpdateHistoryList({
  list,
}: {
  list: Ref<API.TaskUnitVO[]>
}) {
  const timerRef = ref(0)
  const progressCache = ref<Record<number, API.TaskUnitVO>>({})
  const artworkIdsRef = ref<number[]>([])

  const updateData = async () => {
    try {
      const res = await listDrawTaskUnitDetailsByIds({
        drawTaskUnitIds: artworkIdsRef.value,
      })
      const list = (res?.data as API.TaskUnitVO[]) || []
      const _progressCache = cloneDeep(progressCache.value)
      list?.forEach(item => {
        _progressCache[item?.id!] = item
      })

      progressCache.value = _progressCache
      if (!list.filter(it => it?.status === 'GENERATING').length) return
      timerRef.value = window.setTimeout(updateData, 2000)
    } catch (error) {
      console.log(error)
    }
  }

  watch(
    () => list.value,
    () => {
      window.clearTimeout(timerRef.value)
      artworkIdsRef.value =
        list.value
          ?.filter(item => ['GENERATING'].includes(item?.status!))
          ?.map(item => item.id!) ?? []
      if (!artworkIdsRef.value.length) return
      updateData()
    },
    {
      deep: true,
    }
  )

  onBeforeUnmount(() => {
    console.log('unmount')
    window.clearTimeout(timerRef.value)
  })

  return progressCache
}
