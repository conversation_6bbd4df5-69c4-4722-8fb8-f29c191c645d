<template>
  <MenuSwitchDialog
    v-model:visible="dialogVisible"
    @getItems="getArray"
    :key="childKey"
  />
  <div class="selectItems" @click="showSelectDialog">选择</div>
  <div
    style="display: flex; flex-wrap: wrap; margin-top: -15px"
    v-if="tagsArray1.length > 0"
  >
    <div
      v-for="(tag, index) in tagsArray1"
      :key="tag"
      style="margin-right: 5px; margin-bottom: 5px"
    >
      <el-tag closable type="info" @close="handleClose(index)">
        {{ tag.label2 }}
      </el-tag>
    </div>
  </div>
  <div class="my-textarea" :class="class">
    <el-input
      v-model="modelValue"
      type="textarea"
      resize="none"
      :maxlength="maxlength"
      v-bind="$attrs"
    />
    <div class="text-xs text-tertiary" v-if="showCount && maxlength">
      {{ num }}/{{ maxlength }}
    </div>
    <div class="absolute right-2 bottom-2">
      <div style="display: flex; align-items: center">
        <div class="promptHelp" @click="helpClick">
          <img src="/images/helpIcon2.png" alt="" class="helpImg1" />
          <span style="margin-left: 3px">AI润色</span>
        </div>
        <div
          @click="showUpload"
          class="promptHelp"
          v-show="showImgPrompt"
          style="margin-left: 10px"
        >
          <img src="/images/helpIcon.png" alt="" class="helpImg" />
          <span style="margin-left: 3px">图片描述</span>
        </div>
        <MyIcon
          icon="copy"
          :size="16"
          class="cursor-pointer text-tertiary ml-2 transition-all hover:text-brand"
          @click="copy(modelValue ?? '')"
        />
        <MyIcon
          icon="delete1"
          :size="16"
          class="cursor-pointer text-tertiary ml-2 transition-all hover:text-brand"
          @click="$emit('update:modelValue', '')"
        />
      </div>
    </div>
  </div>
  <PictureUpload
    showText="false"
    v-show="dialogUploadVisible"
    v-model:value="uploadImage"
    :drag="true"
    @callBack="callBackFun"
  />
</template>
    <script lang="ts" setup>
import copy from 'copy-to-clipboard'
import { computed, ref } from 'vue'
import { imgProcess, txtProcess } from '@/clients/api/zuohua'
import { ElMessage } from 'element-plus'
interface IStepInputProps {
  maxlength?: number
  showCount?: boolean
  class?: string
  showImgPrompt?: boolean
}
const dialogVisible = ref(false)
const tagsArray1 = ref([])
const { maxlength, showCount = false } = defineProps<IStepInputProps>()
const modelValue = defineModel<string>()
const uploadImage = ref()
const num = computed(() => modelValue.value?.length || 0)
const childKey = ref(0)
const emits = defineEmits(['update:modelValue'])
const originImg = ref()
const dialogUploadVisible = ref(false)
const callBackFun = async e => {
  originImg.value = e
  console.log(originImg.value, '65')
  modelValue.value = '提示词生成中...'
  try {
    let formData = new FormData()
    formData.append('image', originImg.value)
    formData.append(
      'text',
      '作为专业的服装设计师，用英文描述这张图片，重点描述图片中的服装细节，包括面料、剪裁、图案、颜色和任何特殊的设计元素，生成一段流畅的内容作为FLUX文生图模型的提示词。不要包含图片外的内容。生成的示例如下：A female model is wearing a black strapless midi dress crafted from a medium-weight woven fabric with a smooth, slightly matte finish. The bodice features a fitted and structured design with a clean straight neckline and a subtle horizontal seam just below the bust for shaping. The skirt flows into an A-line silhouette with soft, even knife pleats that begin just below the clearly defined waistline, adding volume and movement to the garment. The dress boasts a clean, tailored cut that accentuates the waist and gently flares out to a mid-calf length. The solid black color underscores the classic and elegant design, which is free of any patterns or embellishments. The strapless neckline and pleated skirt are key design elements that contribute to its sophisticated and timeless appeal.'
    )
    formData.append('image_url', uploadImage.value)
    const res = await imgProcess(formData)
    modelValue.value = res.data
  } catch (error: any) {
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
    modelValue.value = ''
  }
}
const helpClick = async () => {
  modelValue.value = '提示词润色中...'
  try {
    const res = await txtProcess({
      text: modelValue.value,
      label_dict: tagsArray1.value,
    })
    modelValue.value = res.data
  } catch (error: any) {
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
    modelValue.value = ''
  }
}
const getArray = (e, value) => {
  console.log(e, '66')
  console.log(value, '88')
  value.forEach(item => {
    tagsArray1.value.push(item)
  })
  console.log(tagsArray1.value, '968')
}
const handleClose = (index: number) => {
  tagsArray1.value.splice(index, 1)
}
const showSelectDialog = () => {
  dialogVisible.value = true
  childKey.value++
}
const showUpload = () => {
  dialogUploadVisible.value = !dialogUploadVisible.value
}
</script>
    <style scoped lang="scss">
.my-textarea {
  margin-top: -8px;
  @apply border border-tertiary rounded-xl relative;
  padding: 14px 8px 8px 16px;

  &:hover {
    border-color: #eaabe6;
  }
  :deep(.el-textarea) {
    .el-textarea__inner {
      height: 120px;
      padding: 0 8px 0 0;
      border: none;
      box-shadow: none;
      background: transparent;
      @apply text-xs font-semibold text-primary placeholder:text-placeholder;
    }
  }
  .promptHelp {
    cursor: pointer;
    width: 70px;
    height: 22px;
    border-radius: 12px;
    border: 1px solid #f5a4e9;
    font-size: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #f5a4e9;
    // vertical-align: middle;
  }
  .promptHelp:hover {
    color: #fff;
    background: rgba(245, 164, 233, 1);
  }
  .promptHelp:hover .helpImg {
    content: url(/images/helpIcon1.png);
  }
  .promptHelp:hover .helpImg1 {
    content: url(/images/helpIcon3.png);
  }
}
</style>
    