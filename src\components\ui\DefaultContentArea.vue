<template>
  <div class="flex flex-col items-center justify-center">
    <MyImage
      class="w-[480px] mb-6"
      :src="selectPath.processImage!"
      :lazy="false"
    />

    <div
      class="text-base text-center text-secondary whitespace-pre-wrap font-normal"
    >
      {{ selectPath.description }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { subMenuMap } from '@/layout/const'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const selectPath = computed(() => {
  const mainPathname = `/${route.path.split('/')[1] || ''}`
  const paths = subMenuMap[mainPathname] || []
  return paths?.filter(it => it.pathname === route.path)?.[0]
})
</script>

<style scoped lang="scss"></style>
