<template>
  <div class="homepage min-w-[1520px]">
    <div class="banner">
      <div
        style="
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(4, 4, 4, 0.15);
          z-index: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
        "
      >
        <div style="text-align: center">
          <div><img src="/images/home/<USER>" alt="" /></div>
          <div style="font-size: 1.5rem; margin-top: 10px; margin-bottom: 60px">
            Taology AI，一键释放你的创造力
          </div>
          <div>
            <el-button
              style="
                width: 15rem;
                height: 3.75rem;
                border-radius: 4.8125rem;
                background: #f5a4e9;
                border: none;
                color: #fff;
                font-size: 1.375rem;
              "
              @click="goText"
              >立即体验</el-button
            >
          </div>
        </div>
      </div>
      <el-carousel style="height: 1080px">
        <el-carousel-item style="height: 1080px">
          <img
            src="/images/home/<USER>"
            alt=""
            style="width: 100%; height: 100%; object-fit: cover"
          />
        </el-carousel-item>
        <el-carousel-item style="height: 1080px">
          <img
            src="/images/home/<USER>"
            alt=""
            style="width: 100%; height: 100%; object-fit: cover"
          />
        </el-carousel-item>
        <el-carousel-item style="height: 1080px">
          <img
            src="/images/home/<USER>"
            alt=""
            style="width: 100%; height: 100%; object-fit: cover"
          />
        </el-carousel-item>
        <el-carousel-item style="height: 1080px">
          <img
            src="/images/home/<USER>"
            alt=""
            style="width: 100%; height: 100%; object-fit: cover"
          />
        </el-carousel-item>
      </el-carousel>
      <!-- <img
        src="@/assets/images/home/<USER>"
        alt=""
        class="absolute top-0 left-0"
      />
      <img
        src="@/assets/images/home/<USER>"
        alt=""
        class="absolute top-6 right-[85px]"
      />
      <img
        src="@/assets/images/home/<USER>"
        alt=""
        class="absolute top-[206px] left-6"
      />
      <div class="w-[1400px] m-auto flex justify-between">
        <div class="pt-[175px]">
          <img src="@/assets/images/home/<USER>" alt="" />
          <div
            class="text-[24px] font-semibold border-2 border-gradient rounded-full px-[105px] py-2 w-[590px] mt-5 ml-6"
          >
            Taology AI，一键释放你的创造力
          </div>
          <GradientButton class="ml-6 mt-[90px]" @click="goText"
            >立即体验</GradientButton
          >
        </div>
        <div class="pt-[110px]">
          <img src="@/assets/images/home/<USER>" alt="" />
        </div>
      </div>
      <img
        src="@/assets/images/home/<USER>"
        alt=""
        class="absolute bottom-5 left-1/2 -translate-x-1/2 animate-bounce"
      /> -->
    </div>
    <div class="bg-[url('@/assets/images/home/<USER>')] bg-cover bg-center">
      <div class="w-[1400px] m-auto pt-[78px] pb-[100px]">
        <div
          class="flex items-center justify-center text-[48px] font-semibold text-primary"
        >
          <div class="text-gradient-primary">让AI为你所用，</div>
          <div>快速提升效率</div>
        </div>
        <div class="mt-6 text-[18px] text-secondary text-center">
          让花型和图案一键矢量化可编辑，数秒解决人工重复劳动！
        </div>
        <Tabs :items="items" v-model:active="active" class="mx-auto mt-8" />
        <div class="w-[1400px] h-[624px] rounded-[40px] card-shadow mt-8">
          <img
            src="@/assets/images/home/<USER>"
            class="size-full"
            v-show="active === 0"
          />
          <img
            src="@/assets/images/home/<USER>"
            class="size-full"
            v-show="active === 1"
          />
          <img
            src="@/assets/images/home/<USER>"
            class="size-full"
            v-show="active === 2"
          />
        </div>

        <div
          class="bg-[url('@/assets/images/home/<USER>')] bg-cover bg-center h-[800px] rounded-[40px] mt-[108px] pt-[77px]"
        >
          <div
            class="text-[48px] leading-[55px] font-semibold text-primary text-center"
          >
            定制化服务功能
          </div>
          <div class="text-[18px] text-secondary text-center mt-6">
            量身打造个性化解决方案，让每一步都贴合你的需求！
          </div>
          <div class="flex items-center gap-8 mt-[56px] px-[100px]">
            <div
              class="flex-1 h-[478px] bg-white rounded-[32px] overflow-hidden flex flex-col items-center pb-10 relative group"
            >
              <div class="w-full h-[318px]">
                <ImageDiff
                  left-image="/images/home/<USER>"
                  right-image="/images/home/<USER>"
                />
              </div>
              <div
                class="text-gradient-primary text-[28px] font-semibold mt-10 group-hover:opacity-0 transition-all duration-300 ease-in-out"
              >
                风格定制
              </div>
              <div
                class="text-[16px] text-primary mt-4 group-hover:opacity-0 transition-all duration-300 ease-in-out"
              >
                结合自身品牌定位，进行个性化风格的训练
              </div>
              <div
                class="h-[162px] flex items-center justify-center absolute bottom-0 left-0 right-0 translate-y-full group-hover:translate-y-0 transition-all duration-300 ease-in-out bg-white"
              >
                <GradientButton @click="goStyle">立即体验</GradientButton>
              </div>
            </div>
            <div
              class="flex-1 h-[478px] bg-white rounded-[32px] overflow-hidden flex flex-col items-center pb-10 relative group"
            >
              <div class="w-full h-[318px]">
                <ImageDiff
                  left-image="/images/home/<USER>"
                  right-image="/images/home/<USER>"
                />
              </div>
              <div
                class="text-gradient-primary text-[28px] font-semibold mt-10 group-hover:opacity-0 transition-all duration-300 ease-in-out"
              >
                模特定制
              </div>
              <div
                class="text-[16px] text-primary mt-4 group-hover:opacity-0 transition-all duration-300 ease-in-out"
              >
                根据品牌调性，定制专属模特，从人种到姿态，满足多样化出图需求。
              </div>
              <div
                class="h-[162px] flex items-center justify-center absolute bottom-0 left-0 right-0 translate-y-full group-hover:translate-y-0 transition-all duration-300 ease-in-out bg-white"
              >
                <GradientButton @click="goModel">立即体验</GradientButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-[url('@/assets/images/home/<USER>')] bg-cover bg-center">
      <div class="w-[1400px] m-auto pt-[78px] py-[116px]">
        <div
          class="flex items-center justify-center text-[48px] font-semibold text-primary"
        >
          <div class="text-gradient-primary">多款AI工具，</div>
          <div>专为设计师定制</div>
        </div>
        <div class="mt-6 text-[18px] text-secondary text-center">
          汇集智能创作工具，提升效率，助力每一个灵感快速落地，让设计更轻松、更专业。
        </div>

        <Coverflow :images="cardImages" class="mt-10" />
      </div>
    </div>
    <div class="bg-[url('@/assets/images/home/<USER>')] bg-cover bg-center">
      <div class="pt-[78px] pb-16">
        <div
          class="flex items-center justify-center text-[48px] font-semibold text-primary"
        >
          <div>强大AI功能，</div>
          <div class="text-gradient-primary">释放创意潜能</div>
        </div>
        <div class="mt-6 text-[18px] text-secondary text-center">
          让技术为灵感服务，AI助力更快实现创意构思，打破局限，让每一个想法都有实现的可能。
        </div>
        <Tabs :items="items1" v-model:active="active1" class="mx-auto mt-8" />
        <div class="mt-[77px]">
          <CardScroller
            v-for="(cards, index) in featureCards"
            :key="index"
            v-show="active1 === index"
          >
            <FeatureCard
              v-for="card in cards"
              :key="card.title"
              v-bind="card"
            />
          </CardScroller>
        </div>
      </div>
    </div>
    <div class="bg-white h-[507px] flex items-center justify-center">
      <div
        class="w-[1400px] py-[70px] px-[67px] rounded-[40px] bg-footer flex items-center justify-between"
      >
        <div class="flex flex-col items-start gap-[42px]">
          <div
            class="flex items-center justify-center text-[48px] font-semibold text-primary"
          >
            <div>立即体验Taology AI，</div>
            <div class="text-gradient-primary">激发无限灵感</div>
          </div>
          <div class="text-[24px] text-secondary">
            技术为灵感服务，AI助力更快实现创意构思，打破局限，让每一个想法都有实现的可能。
          </div>
        </div>
        <GradientButton @click="goText">立即体验</GradientButton>
      </div>
    </div>
    <Footer />
    <div
      v-if="showScrollTop"
      class="fixed bottom-6 right-6 rounded-full bg-white w-[44px] h-[44px] flex items-center justify-center border-secondary border shadow-md cursor-pointer"
      @click="scrollToTop"
    >
      <MyIcon icon="Arrow-up" :size="24" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Footer from '@/layout/Footer.vue'
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const items = ref([
  {
    icon: 'layer',
    title: '图层分离',
  },
  {
    icon: 'flowerPattern',
    title: '花型提取',
  },
  {
    icon: 'square',
    title: '四方连续',
  },
])

const active = ref(0)
const active1 = ref(0)
const items1 = ref([
  {
    title: '美工常用功能',
  },
  {
    title: '业务员/买手',
  },
])

const featureCards = [
  [
    {
      cover: {
        type: 'diff',
        before: '/images/home/<USER>',
        after: '/images/home/<USER>',
      },
      title: '智能抠图',
      desc: '轻松抠出完美边缘，让创意无拘无束！',
      link: '/image',
    },
    {
      cover: {
        type: 'diff',
        before: '/images/home/<USER>',
        after: '/images/home/<USER>',
      },
      title: '分辨率提升',
      desc: '模糊变清晰，细节更惊艳，分辨率提升，让画质超越期待！',
      link: '/image/resup',
    },
    {
      cover: {
        type: 'image',
        src: '/images/home/<USER>',
      },
      title: '局部重绘',
      desc: '针对需要更改的细节手绘图，快速更改局部部件',
      link: '/image/redraw',
    },
    {
      cover: {
        type: 'image',
        src: '/images/home/<USER>',
      },
      title: '图案粘贴',
      desc: '将选定的图案或设计元素粘贴到服装图像上，模拟真实的图案印花或装饰效果',
      link: '',
    },
    {
      cover: {
        type: 'image',
        src: '/images/home/<USER>',
      },
      title: '模特换头',
      desc: '一键换头，百变风格，模特形象随你而定',
      link: '/model',
    },
  ],
  [
    {
      cover: {
        type: 'image',
        src: '/images/home/<USER>',
      },
      title: '特殊工艺替换',
      desc: '自动识别和区分相近工艺，并能够根据不同需求和喜好的花型工艺做一些变动',
      link: '/craft',
    },
    {
      cover: {
        type: 'image',
        src: '/images/home/<USER>',
      },
      title: '以面料换款',
      desc: '用同一块面料能够自动生成出系列款式',
      link: '',
    },
    {
      cover: {
        type: 'image',
        src: '/images/home/<USER>',
      },
      title: '特殊工艺延伸',
      desc: '原款式不变，特殊工艺款型延伸和扩展',
      link: '',
    },
    {
      cover: {
        type: 'image',
        src: '/images/home/<USER>',
      },
      title: '以款换面料',
      desc: '衣服款式不变，替换不同面料',
      link: '',
    },
  ],
] as const

const cardImages = new Array(10).fill(0).map((_, index) => {
  return `/images/home/<USER>
})
const goText = () => {
  router.push('/flux')
}
const goStyle = () => {
  router.push('/styleOrder')
}
const goModel = () => {
  router.push('/model')
}
const showScrollTop = ref(false)

const onScroll = () => {
  const scrollTop = window.scrollY
  showScrollTop.value = scrollTop > document.documentElement.clientHeight
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  })
}

onMounted(() => {
  window.addEventListener('scroll', onScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', onScroll)
})
</script>
<style scoped lang="scss">
::v-deep .el-carousel__indicator--horizontal .el-carousel__button {
  width: 12px;
  height: 12px;
  background: #bfbfbf;
  border: 1px solid #ffffff;
  border-radius: 50%;
  opacity: 0.5;
}
::v-deep .el-carousel__indicator--horizontal.is-active .el-carousel__button {
  width: 12px;
  height: 12px;
  background: #ffffff;
  border-radius: 50%;
  opacity: 1;
}
</style>
<style>
.homepage {
  .banner {
    @apply h-[1080px] bg-[url('@/assets/images/home/<USER>')] bg-cover bg-center relative;
  }

  .border-1 {
    --border-width: 1px;
  }

  .border-2 {
    --border-width: 2px;
  }

  .border-gradient {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: calc(-1 * var(--border-width));
      left: calc(-1 * var(--border-width));
      right: calc(-1 * var(--border-width));
      bottom: calc(-1 * var(--border-width));
      border-radius: 9999px;
      border: var(--border-width) solid transparent;
      background: linear-gradient(
          88.61deg,
          #ff61cf 4.47%,
          #3bffd1 43.86%,
          #318eff 84.16%
        )
        border-box;
      -webkit-mask: linear-gradient(#fff 0 0) padding-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      pointer-events: none;
    }
  }

  .text-gradient {
    background: linear-gradient(90deg, #ff62d1 3.13%, #3ce7dc 100%);
    @apply bg-clip-text text-transparent;
  }

  .text-gradient-primary {
    background: linear-gradient(270deg, #0e8bff 0%, #e380ff 100%);
    @apply bg-clip-text text-transparent;
  }

  .card-shadow {
    box-shadow: 0px 0px 50.7px 0px rgba(167, 158, 191, 0.25);
  }

  .bg-footer {
    background: linear-gradient(
      88.7deg,
      #fdf1f8 0.22%,
      #e8f3fe 50.66%,
      #e5feff 99.69%
    );
  }
}
</style>
