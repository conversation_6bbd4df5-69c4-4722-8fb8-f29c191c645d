<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          label="模特图片"
          @onUrlChange="onUrlChangeFun"
        />
        <el-button
          @click="editImg"
          style="border-radius: 0.5rem"
          v-if="uploadImage"
          >编辑重绘区域</el-button
        >
        <PictureUpload
          v-model:value="uploadImage1"
          :drag="true"
          label="服饰图片"
        />
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
  <PaintModal
    v-model:visible="paintVisible"
    :bottomImage="uploadImage"
    @confirm="sureMask"
    :maskImage="maskImage || undefined"
  />
</template>
<script setup lang="ts">
import { vonModel, QiNiuImgUpload } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, ref, watch, onMounted } from 'vue'
import { toBlob } from '@/utils'
import { useImgwork } from '@/store'
const useImg = useImgwork()
const uploadImage = ref()
const uploadImage1 = ref()
const maskImage = ref()
const confirmLoading = ref(false)
const paintVisible = ref(false)
const activeIndex = ref(0)
const originImg = ref()
const originImg1 = ref()
const maskOne = ref()
const artwork = useArtwork()
const userStore = useUserInfoStore()
const editImg = async () => {
  paintVisible.value = true
  // if (uploadImage.value.includes('api.taologyai.com')) {
  //   try {
  //     let formData = new FormData()
  //     formData.append('image_url', uploadImage.value)
  //     const res = await QiNiuImgUpload(formData)
  //     if (res.data) {
  //       uploadImage.value = res.data
  //     }
  //   } catch (error: any) {
  //     // ElMessage.error(error.message || '出错了')
  //   }
  // }
}
const sureMask = (e: { file: File; url: string }) => {
  maskOne.value = e.file
  maskImage.value = e.url
}
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()
const onUrlChangeFun = () => {
  maskImage.value = null
  maskOne.value = null
}
const handleConfirm = async () => {
  console.log(uploadImage.value, '354')
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传模特图片')
    return
  }
  if (!maskImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请涂抹图片')
    return
  }
  if (!uploadImage1.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传服饰图片')
    return
  }
  try {
    confirmLoading.value = true
    const response = await toBlob(uploadImage.value)
    originImg.value = response
    const response1 = await toBlob(uploadImage1.value)
    originImg1.value = response1
    const maskResponse = await toBlob(maskImage.value)
    maskOne.value = maskResponse
    let formData = new FormData()
    formData.append('image_model', originImg.value)
    formData.append('image_url', uploadImage.value)
    formData.append('image_clothes', originImg1.value)
    formData.append('image_clothes_url', uploadImage1.value)
    formData.append('image_mask', maskOne.value)
    formData.append('image_mask_url', maskImage.value)
    const res = await vonModel(formData)
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}
watch(
  () => useImg.imgUrlModelSwap,
  () => {
    uploadImage.value = useImg.imgUrlModelSwap
    maskImage.value = null
    artwork.creatingArtworks = []
  }
)
watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.change_head_plusFields?.imageUrl
    uploadImage1.value = params?.change_head_plusFields?.imageClothesUrl
    maskImage.value = params?.change_head_plusFields?.imageMaskUrl
  }
)
onMounted(async () => {
  uploadImage.value = useImg.imgUrlModelSwap
})
onBeforeUnmount(() => {
  stopCreatingPoll()
  useImg.setUpdateUrlModelSwap('')
})
</script>
<style scoped lang="scss">
:deep(.el-button:hover) {
  color: #606022;
  background: none;
  border-color: #dcdfe6;
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: #fbdef5;
  border: 1px solid #fbdef5;
  color: #fbdef5;
}

::v-deep .el-checkbox__label {
  color: #dcdfe6;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #fbdef5 !important;
}

::v-deep .el-checkbox__inner:hover {
  border-color: #dcdfe6;
}
</style>
  