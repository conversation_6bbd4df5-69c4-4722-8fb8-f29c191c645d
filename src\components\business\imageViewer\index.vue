<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { EffectFade, Autoplay } from 'swiper/modules'
// @ts-ignore
import 'swiper/css'
// @ts-ignore
import 'swiper/css/effect-fade'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useImgwork } from '@/store'
import { xhrDownload } from '@/utils/file'
import { QiNiuImgUpload } from '@/clients/api/zuohua'
import { ElMessage } from 'element-plus'
import JSZip from 'jszip'
import { saveAs } from 'file-saver'
const handleDownload = async () => {
  console.log(previewImages, '584')
  const zip = new JSZip()
  const promises = []

  files.value.forEach(file => {
    const promise = fetch(file.url)
      .then(response => response.blob())
      .then(blob => {
        zip.file(file.name, blob)
      })
    promises.push(promise)
  })

  await Promise.all(promises)

  zip.generateAsync({ type: 'blob' }).then(content => {
    saveAs(content, 'downloads.zip')
  })

  // if (previewImages.length > 1) {
  //   const zip = new JSZip()
  //   for (let i = 0; i < previewImages.length; i++) {
  //     // 获取图片资源
  //     fetch(previewImages[i].result?.url!)
  //       .then(response => response.blob())
  //       .then(blob => {
  //         // 将Blob添加到ZIP包中，这里假设图片名为'image'+i+'.jpg'
  //         zip.file(`img${i}.jpg`, blob)
  //         // 在所有图片都处理完之后生成ZIP
  //         if (i === previewImages.length - 1) {
  //           zip.generateAsync({ type: 'blob' }).then(content => {
  //             // 下载ZIP文件
  //             saveAs(content, 'downloaded_images.zip')
  //           })
  //         }
  //       })
  //   }
  // }
}
// const urlToBase64 = (url: string) => {
//   return new Promise((resolve, reject) => {
//     const img = new Image()
//     img.crossOrigin = 'Anonymous'
//     img.onload = () => {
//       const canvas = document.createElement('canvas')
//       const ctx = canvas.getContext('2d')!
//       canvas.width = img.width
//       canvas.height = img.height
//       ctx.drawImage(img, 0, 0)
//       const dataURL = canvas.toDataURL('image/jpeg')
//       resolve(dataURL)
//     }
//     img.onerror = e => {
//       reject(e)
//     }
//     img.src = `${url}?x-oss-process=image/resize,w_800`
//   })
// }
const useImg = useImgwork()
const jumpRoute = async (path: string, url: string) => {
  router.replace({
    path: path,
  })
  // if (url.includes('************')) {
  //   try {
  //     let formData = new FormData()
  //     formData.append('image_url', url)
  //     const res = await QiNiuImgUpload(formData)
  //     newUrl = res.data
  //   } catch (error: any) {
  //     ElMessage.error(error.message || '出错了')
  //   }
  // } else {
  //   newUrl = url
  // }
  if (path === '/craft') {
    useImg.setUpdateUrlCraft(url)
  } else {
    useImg.setUpdateUrl(url)
  }
  // urlToBase64(url).then((res: any) => {
  //   console.log(res, '6845')
  //   useImg.setUpdateUrl(res)
  //   useImg.setUpdateUrlCraft(res)
  // })
}
interface ImageViewerProps {
  previewImages: API.TaskUnitDetailVO[]
  autoplay?: boolean
  activeIndex?: number
}

const {
  previewImages,
  autoplay = false,
  activeIndex = 0,
} = defineProps<ImageViewerProps>()
const emits = defineEmits(['slideChange'])
const router = useRouter()
// 当前查看的图片
const currentPicture = ref<API.TaskUnitDetailVO | null>()
const visible = ref(false)
</script>
<template>
  <div
    class="w-full h-full"
    :class="[{ '-mt-[60px]': previewImages.length > 1 }]"
  >
    <swiper
      :slides-per-view="1"
      :modules="[EffectFade, Autoplay]"
      effect="fade"
      :navigation="false"
      :loop="false"
      :autoplay="
        autoplay
          ? {
              delay: 5000,
              disableOnInteraction: true,
              pauseOnMouseEnter: true,
            }
          : false
      "
      :fadeEffect="{ crossFade: true }"
      :allowTouchMove="false"
      @slideChange="
        ({ activeIndex }: { activeIndex: number }) => {
          emits('slideChange', activeIndex)
        }
      "
    >
      <div
        style="position: absolute; bottom: 0; z-index: 99; cursor: pointer"
        @click="handleDownload"
      >
        下载
      </div>
      <swiper-slide v-for="image in previewImages" :key="image?.result?.url">
        <MyImage
          v-if="image.status === 'SUCCESS'"
          :src="image?.result?.url!"
          class="w-full h-full flex items-center justify-center"
          @click="
            () => {
              currentPicture = image
              visible = true
            }
          "
        />
        <div
          v-else-if="image.status === 'GENERATING'"
          class="w-full h-full flex items-center justify-center"
        >
          <div class="text-center">
            <MyImage
              :lazy="false"
              src="/images/loading.png"
              class="animate-spin w-[42px] h-[42px] mx-auto"
            />
            <div class="text-tertiary text-sm mt-2.5">
              正在生成中
              {{ Number((image?.completePercent || 0) * 100).toFixed(2) }}%
            </div>
          </div>
        </div>
        <div
          v-else
          class="w-full h-full flex items-center justify-center"
          @click="
            () => {
              currentPicture = image
              visible = true
            }
          "
        >
          <div class="text-center">
            <MyIcon icon="Warning" :size="42" class="text-tertiary" />
            <div class="text-tertiary text-sm mt-2.5">生成失败</div>
          </div>
        </div>
      </swiper-slide>

      <Navigation
        v-if="previewImages.length > 1"
        :previewImages="previewImages"
        :activeIndex="activeIndex"
      />
    </swiper>
  </div>
  <ImageDetailModal v-model:visible="visible" v-model:detail="currentPicture" />
</template>
<style scoped lang="scss">
.swiper {
  @apply relative rounded-lg overflow-visible w-full h-full;
  :deep(.swiper-slide) {
    @apply w-full h-full overflow-hidden rounded-lg bg-white border-2 border-dashed border-neutral-200;
  }
  .buttonJump {
    position: absolute;
    z-index: 99;
    bottom: 10px;
    right: 120px;
    width: 100px;
    height: 40px;
    font-size: 14px;
    line-height: 40px;
    border: 1px solid #f5a4e9;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    color: #f5a4e9;
  }
}
</style>
