<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { default as api } from '@/clients/api'
import { ElMessage } from 'element-plus'
import MyButton from '@/components/ui/MyButton.vue'
import Help from '@/components/ui/Help.vue'
import MyDialog from '@/components/ui/MyDialog.vue'
import StepInputNumber from '@/components/ui/StepInput.vue'
import MyTextarea from '@/components/ui/MyTextarea.vue'
import MyInput from '@/components/ui/MyInput.vue'

onMounted(async () => {
  try {
    const res = await api.zuohua.styleExtension({
      num: 1,
      drawParam: {
        initImage:
          'https://sfile.chatglm.cn/testpath/2023/11/15/6378b9b9-9f5f-4e9f-8e2b-8c7c9c5b8d8c.jpg',
        maskImage: 0,
        prompt: '请将图片中的内容进行风格化处理，使其更具艺术感。',
        patternDensity: 0.5,
      },
    })
  } catch (error) {
    console.log(error)
    ElMessage({
      message: 'this is a error message.',
      type: 'error',
    })
  }
})

const visible = ref(false)
const sliderValue = ref(1)
const sliderRangValue = ref(0)
const inputVal = ref()
const textareaValue = ref('')
const myInput = ref('')
const uploadImage = ref('')
</script>

<template>
  <div class="space-y-4 px-8">
    <my-icon icon="text2image" :size="40" color="--neutral-500"></my-icon>
    <MyButton type="primary" size="lg">按钮大</MyButton>
    <MyButton type="primary">按钮中</MyButton>
    <MyButton type="primary" size="sm">按钮小</MyButton>
    <MyButton :rounded="false">立即生成</MyButton>
    <MyButton type="form" icon="import">从图像解析选择输入</MyButton>

    <picture-upload
      v-model:value="uploadImage"
      :drag="true"
      @onUrlChange="
        url => {
          uploadImage = url
        }
      "
    />
    <div class="w-[500px]">
      <my-slider
        v-model="sliderValue"
        :min="10"
        :max="50"
        :step="0.2"
        show-input
        show-tooltip
      ></my-slider>
      <div>{{ sliderValue }}</div>
    </div>
    <div class="w-[500px]">
      <my-slider
        v-model="sliderRangValue"
        :min="10"
        :max="50"
        :step="10"
        show-tooltip
        show-stops
        :format-tooltip="
          value => {
            let tag = '超小'
            if (value > 10) {
              tag = '小'
            }
            if (value > 20) {
              tag = '中'
            }
            if (value > 30) {
              tag = '大'
            }
            if (value > 40) {
              tag = '超大'
            }
            return tag
          }
        "
      ></my-slider>
      <div>{{ sliderRangValue }}</div>
    </div>
    <Help
      content="Help内容Help内容Help内容Help内容Help内容Help内容Help内容Help内容Help内容"
    />
    <MyButton @click="visible = true"> 点击打开Dialog </MyButton>

    <!-- <DefaultContentArea
      >将选定的图案或设计元素粘贴到服装图像上，模拟真实的图案印花或装饰效果。</DefaultContentArea
    > -->
    <my-dialog
      v-model:visible="visible"
      title="Dialog Title"
      @close="visible = false"
      @confirm="visible = false"
    >
      <div>
        弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容
        弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容
        弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容弹窗内容
      </div>
    </my-dialog>
    <div class="bg-linear-primary w-[100px] h-[24px]">222222</div>
    <div class="flex items-center">
      <div class="w-[1000px] h-[500px] flex">
        <Paint />
      </div>
      <div></div>
    </div>

    <StepInputNumber class="mt-4" v-model="inputVal" :max="10" :step="2" />
    <MyTextarea
      v-model="textareaValue"
      placeholder="请输入"
      class="w-[500px] mt-4"
      :maxlength="20"
      :showCount="true"
    />
    <div class="w-[500px] mt-4">
      <MyInput placeholder="请输入" v-model="myInput">
        <template #suffix>
          <div class="text-neutral-900">| 忘记密码</div>
        </template>
      </MyInput>
    </div>
  </div>

  <div>home</div>
</template>

<style scoped></style>
