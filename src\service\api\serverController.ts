// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** 此处后端没有提供注释 POST /server/manage */
export async function serverAdd(
  body: API.ServerInfoParam,
  options?: { [key: string]: any }
) {
  return request<API.ResultMapStringBoolean>('/server/manage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}
