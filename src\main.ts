import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import { router } from './router'
import '@/styles/global.css'
import 'element-plus/dist/index.css'
import '@/styles/fonts/iconfont.css'

import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import lazyPlugin from 'vue3-lazy'

const pinia = createPinia()
const app = createApp(App)

app.use(router)
app.use(pinia)
app.use(lazyPlugin, {
  loading: '/images/placeholder.png', // 图片加载时默认图片
  error: '/images/placeholder.png', // 图片加载失败时默认图片
})
app.mount('#app')

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
// // 初始化语言
// if (navigator.language == 'zh-CN') {
//   window.location.href = "http://172.16.0.83:8888/en"
// }

