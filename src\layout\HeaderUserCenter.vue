<script setup lang="ts">
import MyIcon from '@/components/ui/MyIcon.vue'
import { ElDivider } from 'element-plus'
import { useUserInfoStore } from '@/store'
import { router } from '@/router'
import { UserFilled } from '@element-plus/icons-vue'
const userInfo = useUserInfoStore()

const onLogout = () => {
  userInfo.logout()
}
</script>
<template>
  <el-popover
    placement="bottom"
    :width="240"
    trigger="hover"
    popper-class="headerUserCenter"
    :show-arrow="false"
    v-if="userInfo.logined"
  >
    <div>
      <div class="flex items-center">
        <el-avatar
          :size="48"
          :src="userInfo.base?.avatar"
          class="flex-shrink-0"
        />
        <div class="ml-4 flex-auto overflow-hidden">
          <div class="flex items-center">
            <div
              class="text-sm font-semibold whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {{ userInfo.base?.nickname }}
            </div>
            <MyIcon
              icon="share"
              :size="16"
              class="flex-shrink-0 ml-2.5 cursor-pointer"
              @click="userInfo.setUpdateProfileVisible(true)"
            />
          </div>
          <div class="text-xs whitespace-nowrap overflow-hidden text-ellipsis">
            <span>剩余作画张数</span>
            <strong class="mx-2 font-semibold">{{
              userInfo.base?.drawingTimes
            }}</strong>
            <span>张</span>
          </div>
        </div>
      </div>

      <el-divider class="!my-4 opacity-[0.08] !border-neutral-950" />
      <!-- <div class="headerUserCenter_item" @click="router.push('/mine')">
        <MyIcon icon="pic-mine" :size="16" class="mr-2" />
        <span>我的创作</span>
      </div> -->
      <div
        class="headerUserCenter_item"
        @click="userInfo.setSettingLoginType('email')"
      >
        <MyIcon icon="email" :size="16" class="text-neutral-700 mr-2" />
        <span>邮箱设置</span>
      </div>
      <div
        class="headerUserCenter_item !mb-1"
        @click="userInfo.setSettingLoginType('password')"
      >
        <MyIcon icon="lock" :size="16" class="text-neutral-700 mr-2" />
        <span v-if="userInfo.base.passwordInitialized">重置密码</span>
        <span v-else>密码设置</span>
      </div>
      <el-divider class="!my-4 opacity-[0.08] !border-neutral-950" />
      <div class="headerUserCenter_item !mb-0" @click="onLogout">
        <MyIcon icon="logout" :size="16" class="text-neutral-700 mr-2" />
        <span>退出登录</span>
      </div>
    </div>

    <template #reference>
      <el-avatar :size="40" :src="userInfo.base?.avatar" :icon="UserFilled" />
    </template>
  </el-popover>
  <div @click="userInfo.setLoginType('code')" v-else>
    <img
      class="w-[40px] h-[40px] rounded block"
      src="/images/user-avatar-default.png"
      alt=""
    />
  </div>
</template>
<style lang="scss">
.headerUserCenter.headerUserCenter {
  @apply rounded-lg border-0 px-4 py-6;
  background: linear-gradient(219deg, #fadff0 0.94%, #fff 54.87%);
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.08);

  .headerUserCenter_item {
    @apply h-6 flex items-center text-neutral-700 mb-4 cursor-pointer;
  }
  .headerUserCenter_item:hover {
    opacity: 0.8;
  }
}
</style>
