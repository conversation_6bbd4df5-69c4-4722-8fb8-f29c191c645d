<script setup lang="ts">
import Header from './Header.vue'
import { computed, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

import { useUserInfoStore } from '@/store'

const userInfo = useUserInfoStore()

const activeIndex = ref('')
const router = useRouter()
const route = useRoute()

onMounted(userInfo.getUserInfo)
</script>

<template>
  <div>
    <Header headerType="home" />
    <router-view />
  </div>

  <LoginModal :visible="!!userInfo.loginType" v-if="!!userInfo.loginType" />
  <SettingLoginModal
    :visible="!!userInfo.settingLoginType"
    v-if="!!userInfo.settingLoginType"
  />
  <UpdateProfileModal
    :visible="userInfo.updateProfileVisible"
    v-if="userInfo.updateProfileVisible"
  />
  <ValidateAccountModal
    :visible="!!userInfo.validateAccountInfo"
    v-if="!!userInfo.validateAccountInfo"
  />
</template>

<style scoped lang="scss"></style>
