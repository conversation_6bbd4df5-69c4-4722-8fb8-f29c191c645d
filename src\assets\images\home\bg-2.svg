<svg width="1920" height="1951" viewBox="0 0 1920 1951" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_940_12211)">
<g filter="url(#filter0_f_940_12211)">
<path d="M167.49 623.505C-30.8506 274.853 329.588 142.565 365.831 106.305C107.351 -266.653 -529.491 106.305 -403.237 479.263C-276.984 852.221 365.831 972.157 167.49 623.505Z" fill="#D9FDFF"/>
</g>
<g filter="url(#filter1_f_940_12211)">
<path d="M395.433 454.809C238.972 179.902 523.304 75.5944 551.895 47.0041C347.992 -247.068 -154.382 47.0041 -54.7867 341.076C44.8083 635.149 551.895 729.717 395.433 454.809Z" fill="#6DB3F8" fill-opacity="0.21"/>
</g>
<g filter="url(#filter2_f_940_12211)">
<path d="M-18.7432 565.421C93.8558 495.308 353.739 450.083 492.48 830.087C665.905 1305.09 -529.966 1003.51 -18.7432 565.421Z" fill="#FACFE9" fill-opacity="0.62"/>
</g>
<g filter="url(#filter3_f_940_12211)">
<path d="M1686.83 -114.243C1799.42 -184.356 2059.31 -229.581 2198.05 150.423C2371.47 625.428 1175.6 323.85 1686.83 -114.243Z" fill="#FACFE9" fill-opacity="0.3"/>
</g>
<g filter="url(#filter4_f_940_12211)">
<path d="M427.081 1505.37C539.68 1435.25 799.563 1390.03 938.304 1770.03C1111.73 2245.04 -84.1419 1943.46 427.081 1505.37Z" fill="#FACFE9" fill-opacity="0.62"/>
</g>
<g filter="url(#filter5_f_940_12211)">
<path d="M1775.85 946C1748.37 982.638 1286.86 1056.62 1370.01 1537.15C1453.15 2017.67 2097.84 1249.68 1775.85 946Z" fill="url(#paint0_linear_940_12211)" fill-opacity="0.56"/>
</g>
</g>
<defs>
<filter id="filter0_f_940_12211" x="-862.619" y="-491.179" width="1671.45" height="1765.08" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="221.5" result="effect1_foregroundBlur_940_12211"/>
</filter>
<filter id="filter1_f_940_12211" x="-361.709" y="-368.805" width="1207.6" height="1281.14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="147" result="effect1_foregroundBlur_940_12211"/>
</filter>
<filter id="filter2_f_940_12211" x="-469.024" y="195.853" width="1301.06" height="1197.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="161.1" result="effect1_foregroundBlur_940_12211"/>
</filter>
<filter id="filter3_f_940_12211" x="1236.54" y="-483.811" width="1301.06" height="1197.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="161.1" result="effect1_foregroundBlur_940_12211"/>
</filter>
<filter id="filter4_f_940_12211" x="-23.2" y="1135.8" width="1301.06" height="1197.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="161.1" result="effect1_foregroundBlur_940_12211"/>
</filter>
<filter id="filter5_f_940_12211" x="1129" y="715" width="967.281" height="1207.42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="115.5" result="effect1_foregroundBlur_940_12211"/>
</filter>
<linearGradient id="paint0_linear_940_12211" x1="1413.64" y1="1223.54" x2="1612.64" y2="1691.42" gradientUnits="userSpaceOnUse">
<stop stop-color="#C5ABF2" stop-opacity="0.27"/>
<stop offset="1" stop-color="#C5ABF2"/>
</linearGradient>
<clipPath id="clip0_940_12211">
<rect width="1920" height="1951" fill="white"/>
</clipPath>
</defs>
</svg>
