// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** 此处后端没有提供注释 POST /api/v1/draw/simple_draw */
export async function simpleDraw(
  body: API.SimpleDrawReq,
  options?: { [key: string]: any }
) {
  return request<API.ResultArtworkCreateResultVO>('/api/v1/draw/simple_draw', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}
