import { listDrawTaskUnitDetailsByIds } from '@/clients/api/zuohua'
import { useArtwork } from '@/store'
import { ref } from 'vue'

let timer: any = null

export function useCreatingPoll() {
  const loading = ref(false)
  const artwork = useArtwork()

  const pollCreatingArtworks = ({
    ids,
    isUpdate = true,
    callback,
  }: {
    ids: number[]
    isUpdate?: boolean
    callback?: (list: API.TaskUnitVO[]) => void
  }) => {
    window.clearTimeout(timer)

    listDrawTaskUnitDetailsByIds({
      drawTaskUnitIds: ids,
    }).then(res => {
      const list = (res?.data as API.TaskUnitVO[]) || []
      if (
        /** 生成中 */
        !!list.filter(it => it?.status === 'GENERATING').length
      ) {
        loading.value = true
        artwork.setCreatingArtworks(list)
        if (isUpdate) {
          artwork.setUpdateHistory(true)
        }
        timer = window.setTimeout(() => {
          pollCreatingArtworks({
            ids,
            isUpdate: false,
            callback,
          })
        }, 2000)
      } else {
        /** 生成完成/失败 */
        loading.value = false
        artwork.setCreatingArtworks(list)
        callback?.(list)
        // TODO 更新次数
        window.clearTimeout(timer)
      }
    })
  }

  const stopCreatingPoll = () => {
    window.clearTimeout(timer)
  }

  const setLoading = (value: boolean) => {
    loading.value = value
  }

  return {
    pollCreatingArtworks,
    stopCreatingPoll,
    loading,
    setLoading,
  }
}
