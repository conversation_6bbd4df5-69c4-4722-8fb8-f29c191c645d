<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <FormItem label="画面描述">
          <MyTextarea
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <FormItem label="相似度">
          <MySlider :max="9" :min="1" v-model="sliderStep" show-input />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { styleSketch2cloth } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, ref, watch } from 'vue'
import { toBlob } from '@/utils'
const uploadImage = ref<string | null | undefined>()
const prompt = ref('')
const confirmLoading = ref(false)
const activeIndex = ref(0)
const originImg = ref()
const artwork = useArtwork()
const userStore = useUserInfoStore()
const sliderStep = ref(7)
const count = ref(2)
const num = ref(0)
const selectChange = (e: number) => {
  count.value = e
}
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()
const handleConfirm = async () => {
  console.log(uploadImage.value, '354')
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }
  num.value = 0
  let step
  switch (sliderStep.value) {
    case 9:
      step = 1
      break
    case 8:
      step = 2
      break
    case 7:
      step = 3
      break
    case 6:
      step = 4
      break
    case 5:
      step = 5
      break
    case 4:
      step = 6
      break
    case 3:
      step = 7
      break
    case 2:
      step = 8
      break
    case 1:
      step = 9
      break
  }
  try {
    confirmLoading.value = true
    const response = await toBlob(uploadImage.value)
    originImg.value = response
    let formData = new FormData()
    formData.append('image_load', originImg.value)
    formData.append('image_url', uploadImage.value)
    formData.append('prompt', prompt.value)
    formData.append('similar', String(step))
    formData.append('n_iter', String(count.value))
    const res = await styleSketch2cloth(formData)
    confirmLoading.value = false
    activeIndex.value = 0
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.change_head_plusFields?.imageUrl
    prompt.value = params?.change_head_plusFields?.prompt || ''
    count.value = params?.change_head_plusFields?.n_iter
    switch (params?.change_head_plusFields?.similar) {
      case 9:
        sliderStep.value = 1
        break
      case 8:
        sliderStep.value = 2
        break
      case 7:
        sliderStep.value = 3
        break
      case 6:
        sliderStep.value = 4
        break
      case 5:
        sliderStep.value = 5
        break
      case 4:
        sliderStep.value = 6
        break
      case 3:
        sliderStep.value = 7
        break
      case 2:
        sliderStep.value = 8
        break
      case 1:
        sliderStep.value = 9
        break
    }
  }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss"></style>
    