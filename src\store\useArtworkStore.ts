import { defineStore } from 'pinia'
/**
 * 历史记录状态，用于作画时，历史记录自动更新
 *
 */

export const useArtwork = defineStore('artwork', {
  state: () =>
    <
      {
        /** 创作更新侧边历史记录 */
        isUpdate: boolean
        /** 正在创作中的任务/历史记录选中还原的任务 */
        creatingArtworks: API.TaskUnitVO[]
        drawParams: API.TaskUnitDetailVO | null
      }
    >{
      isUpdate: false,
      creatingArtworks: [],
      drawParams: null,
    },
  actions: {
    setUpdateHistory(status: boolean) {
      this.isUpdate = status
    },
    setCreatingArtworks(artworks: API.TaskUnitVO[]) {
      this.creatingArtworks = artworks
    },
    setDrawParams(params: API.TaskUnitDetailVO | null) {
      this.drawParams = params
    },
  },
})
