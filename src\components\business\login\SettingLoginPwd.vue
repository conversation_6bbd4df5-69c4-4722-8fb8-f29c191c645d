<script setup lang="ts">
import { reactive, getCurrentInstance, ref } from 'vue'
import { FormRules, FormInstance, ElMessage } from 'element-plus'
import {
  isMobileReg,
  passwordSaveReg,
  showMobile,
  showGlobalError,
} from '@/utils'
import Captcha, { type ValidInfoProps } from './Captcha.vue'
import {
  authSendVerificationCode,
  authResetPassword,
} from '@/clients/api/authentication'
import MyButton from '@/components/ui/MyButton.vue'
import MyInput from '@/components/ui/MyInput.vue'
import CryptoJS from 'crypto-js'
import { useUserInfoStore } from '@/store'

const emits = defineEmits(['success'])
const userStore = useUserInfoStore()

// 当前组件实例 -- 获取form的ref
const instance = getCurrentInstance()
const showMobileRef = ref(
  showMobile(userStore.base.phone || userStore.forgotPwdPhone)
)
const form = reactive({
  mobile: userStore.base.phone || userStore.forgotPwdPhone,
  verificationCode: '',
  password: '',
})
const loading = ref(false)

const validateMobile = (_: any, value: any, callback: any) => {
  // 带 * 号的手机号不验证
  if (!isMobileReg.test(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}

const validatePWd = (_: any, value: any, callback: any) => {
  if (value.length < 6) {
    callback(new Error('密码至少6位'))
  } else if (!passwordSaveReg.test(value)) {
    callback(new Error('密码需包含大小写和数字'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules<typeof form>>({
  mobile: [
    { required: true, message: '请输入手机号' },
    { validator: validateMobile, trigger: 'blur' },
  ],
  verificationCode: [{ required: true, message: '请输入验证码' }],
  password: [
    { required: true, message: '请输入密码' },
    { validator: validatePWd, trigger: 'blur' },
  ],
})

const onCaptchaSuccess = (validInfo: ValidInfoProps) => {
  // success 之后发送验证码
  authSendVerificationCode({
    type: 'PHONE',
    contact: form.mobile,
    source: 'PASSWORD_RESET',
    behaviorVerifyInfo: {
      token: validInfo.token,
      authCode: validInfo.authenticate,
    },
  }).then(resp => {
    if (resp?.data) {
      ElMessage.success('验证码已发送！')
    }
  })
}

const beforeStart = async (next: () => void) => {
  const formRef = instance?.refs?.formRef as FormInstance
  await formRef.validateField('mobile')
  next()
}

const onSubmit = async () => {
  if (loading.value) {
    return
  }
  const formRef = instance?.refs?.formRef as FormInstance
  if (!formRef) return
  const valid = await formRef.validate()
  if (!valid) {
    return
  }
  loading.value = true
  try {
    const resp = await authResetPassword({
      phone: form.mobile,
      verificationCode: form.verificationCode,
      newPassword: CryptoJS.MD5(form.password).toString(),
    })
    if (resp?.data) {
      ElMessage.success('密码设置成功')
      emits('success')
      userStore.setForgotPwdPhone('')
    }
  } catch (e) {
    showGlobalError(e, '出错了，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>
<template>
  <el-form
    ref="formRef"
    label-position="top"
    :model="form"
    :rules="rules"
    class="login-form"
  >
    <el-form-item label="手机号" prop="mobile">
      <my-input
        placeholder="请输入手机号"
        v-model="form.mobile"
        :maxlength="11"
        v-if="!showMobileRef"
      />
      <my-input :model-value="showMobileRef" disabled v-else />
    </el-form-item>
    <el-form-item label="验证码" prop="verificationCode">
      <my-input placeholder="请输入验证码" v-model="form.verificationCode">
        <template #suffix>
          <Captcha :beforeStart="beforeStart" @success="onCaptchaSuccess" />
        </template>
      </my-input>
    </el-form-item>
    <div class="h-0 overflow-hidden opacity-0">
      <my-input
        placeholder="fix: 浏览器密码自动填充到这里而不至于填充到验证码上"
      />
    </div>
    <el-form-item
      :label="userStore.forgotPwdPhone ? '新密码' : '密码'"
      prop="password"
    >
      <my-input
        placeholder="请输入密码"
        v-model="form.password"
        type="password"
        autocomplete="new-password"
        maxlength="18"
      />
    </el-form-item>
  </el-form>
  <div>
    <my-button
      type="primary"
      class="w-full"
      @click="onSubmit"
      :loading="loading"
      >确定</my-button
    >
  </div>
</template>
