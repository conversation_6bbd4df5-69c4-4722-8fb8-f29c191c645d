<script setup lang="ts">
import { ElDatePicker, ElSelect, ElOption } from 'element-plus'
import { reactive, watch } from 'vue'

const { filterForm } = defineProps<{ filterForm: any }>()
const typeOptions = [
  {
    value: 0,
    label: '全部',
  },
  {
    value: 4, // LAYER_SEPARATOR
    label: '图层分离',
  },
  {
    value: 5, // FLOWER_PATTERN_EXTRACTION(
    label: '花型提取',
  },
  {
    value: 6, // PATTERN_CYCLE
    label: '四方连续',
  },
  {
    value: 7, // FABRIC_PATTERN_CREATION
    label: '面料花型创作',
  },
  {
    value: 8, // STYLE_EXTENSION
    label: '款式延伸',
  },
  {
    value: 9, // PATTERN_REPLACE
    label: '以款换面料',
  },
  {
    value: 10, // FABRIC_REPLACE
    label: '图案粘贴',
  },
] as const

const handleBack = () => {
  history.back()
}
</script>

<template>
  <div
    class="flex justify-between items-center whitespace-nowrap px-4 pt-6 pb-4"
  >
    <h3 class="text-[#23262F] font-bold text-[28px] leading-[40px]">
      我的创作
    </h3>
    <div class="flex items-center">
      <ElDatePicker
        type="daterange"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        range-separator="至"
        v-model="filterForm.dateRange"
        class="!bg-transparent !h-10 !rounded-lg"
      />
      <ElSelect
        placeholder="作画类型"
        class="select-type"
        v-model="filterForm.type"
        :clearable="true"
      >
        <ElOption
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :class="{ 'is-selected': !item.value && !filterForm.type }"
        />
      </ElSelect>
      <MyButton
        type="primary"
        @click="handleBack"
        class="!h-10 !min-w-[108px] !rounded-lg ml-4 !text-xs"
        >返回</MyButton
      >
    </div>
  </div>
</template>

<style scoped lang="scss">
.select-type {
  @apply w-[130px] h-10 ml-6;
  --el-border-color: var(--neutral-300);
  :deep(.el-select__wrapper) {
    @apply h-full bg-transparent rounded-lg;
  }
  :deep(.el-select__placeholder.is-transparent) {
    color: #171717;
  }
  :deep(.el-select__wrapper.is-focused) {
    --el-color-primary: var(--primary);
  }
}
</style>
