

<template>
  <div class="my-select">
    <el-select
      :placeholder="placeholder"
      v-model="selectVal"
      @change="selectChange"
      :teleported="false"
    >
      <el-option
        v-for="option in list"
        :key="option.value"
        :value="option.value"
        :label="option.label"
      >
      </el-option>
    </el-select>
  </div>
</template>
  <script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
interface ISelectProps {
  placeholder?: string
  list: Array<any>
  modelValue?: string
}
const emits = defineEmits(['update:modelValue', 'onChangeSelect'])
const { placeholder, list, modelValue } = defineProps<ISelectProps>()
const selectVal = computed({
  get() {
    return modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  },
})

const selectChange = (event: string) => {
  emits('onChangeSelect', event)
}
</script>
  <style scoped lang="scss">
.my-select {
  //   @apply border border-tertiary rounded-xl relative;
  @apply border rounded-xl;
  &:hover {
    border-color: #eaabe6;
  }
  :deep(.el-select) {
    .el-select__wrapper {
      //   padding: 0 8px 0 0;
      border: none;
      box-shadow: none;
      background: transparent;
    }
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #fadff0;
    }
    .el-select-dropdown__item {
      color: #606266;
      background: transparent;
    }
  }
}
</style>
  