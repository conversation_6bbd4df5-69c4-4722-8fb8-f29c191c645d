<script setup lang="ts">
import { isSvg } from '@/utils'
import { CSSProperties } from 'vue'
import { onMounted } from 'vue'

interface IProps {
  src: string
  style?: CSSProperties
  quality?: number
  alt?: string
  lazy?: boolean
  tag?: 'v64' | 'v400' | 'v800' | 'v1200' | 'v2400'
  onClick?: () => void
  imgClassName?: string
}

const {
  src = '',
  style,
  quality = 90,
  alt = '',
  lazy = true,
  onClick,
  tag,
  imgClassName,
} = defineProps<IProps>()

const myLoader = ({ src, quality }: { src: string; quality: number }) => {
  if (!src) return '/images/placeholder.png'
  if (isSvg(src)) {
    return src
  }
  if (src.includes('Expires')) return src
  return tag
    ? `${src}?imageView2/2/w/${tag.replace('v', '')}/q/${quality}/format/webp`
    : src
}
</script>

<template>
  <div class="overflow-hidden" :style="style" @click="onClick">
    <img
      v-if="lazy"
      v-lazy="myLoader({ src, quality })"
      layout="fill"
      objectFit="cover"
      class="block max-w-full max-h-full mx-auto relative"
      :alt="alt"
      :class="imgClassName"
    />
    <img
      v-else
      :src="myLoader({ src, quality })"
      layout="fill"
      objectFit="cover"
      class="block max-w-full max-h-full mx-auto relative"
      :alt="alt"
      :class="imgClassName"
    />
  </div>
</template>

<style scoped lang="scss"></style>
