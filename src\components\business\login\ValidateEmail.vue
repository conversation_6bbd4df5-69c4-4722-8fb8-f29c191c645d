<script setup lang="ts">
import { reactive, getCurrentInstance, ref } from 'vue'
import { FormRules, FormInstance, ElMessage } from 'element-plus'
import {
  isEmailReg,
  setAuth,
  showEmail,
  showGlobalError,
  showMobile,
} from '@/utils'
import Captcha, { type ValidInfoProps } from './Captcha.vue'
import {
  authSendVerificationCode,
  authLogin,
} from '@/clients/api/authentication'
import MyButton from '@/components/ui/MyButton.vue'
import MyInput from '@/components/ui/MyInput.vue'
import { useUserInfoStore } from '@/store'

const emits = defineEmits(['success'])
// 当前组件实例 -- 获取form的ref
const instance = getCurrentInstance()
const userStore = useUserInfoStore()
const showEmailRef = ref(showEmail(userStore.validateAccountInfo?.email))
const form = reactive({
  email: userStore.validateAccountInfo?.email,
  verificationCode: '',
})
const uploading = ref(false)

const validateEmail = (_: any, value: any, callback: any) => {
  if (!isEmailReg.test(value)) {
    callback(new Error('请输入邮箱号'))
  } else {
    callback()
  }
}
const rules = reactive<FormRules<typeof form>>({
  email: [
    { required: true, message: '请输入邮箱号' },
    { validator: validateEmail, trigger: 'blur' },
  ],
  verificationCode: [{ required: true, message: '请输入验证码' }],
})

const onCaptchaSuccess = (validInfo: ValidInfoProps) => {
  // success 之后发送验证码
  authSendVerificationCode({
    type: 'EMAIL',
    contact: form.email,
    source: 'LOGIN',
    behaviorVerifyInfo: {
      token: validInfo.token,
      authCode: validInfo.authenticate,
    },
  }).then(resp => {
    if (resp?.data) {
      ElMessage.success('验证码已发送！')
    }
  })
}

const beforeStart = async (next: () => void) => {
  const formRef = instance?.refs?.formRef as FormInstance
  await formRef.validateField('email')
  next()
}

const onSubmit = async () => {
  if (uploading.value) {
    return
  }
  const formRef = instance?.refs?.formRef as FormInstance
  if (!formRef) return
  const valid = await formRef.validate()
  if (!valid) {
    return
  }
  uploading.value = true
  try {
    const resp = await authLogin({
      loginMethod: 'EMAIL_SECURITY_VERIFICATION',
      username: form.email,
      verificationCode: form.verificationCode,
    })
    setAuth(resp?.data || '')
    emits('success')
    ElMessage.success('验证成功')
  } catch (e) {
    showGlobalError(e, '出错了，请稍后重试')
  }
  uploading.value = false
}
</script>
<template>
  <el-form
    ref="formRef"
    label-position="top"
    :model="form"
    :rules="rules"
    class="login-form"
  >
    <el-form-item label="邮箱号" prop="email">
      <my-input
        placeholder="请输入邮箱号"
        v-model="form.email"
        autocomplete="on"
        v-if="!showEmailRef"
      />
      <my-input :model-value="showEmailRef" disabled v-else />
    </el-form-item>
    <el-form-item label="验证码" prop="verificationCode">
      <my-input placeholder="请输入验证码" v-model="form.verificationCode">
        <template #suffix>
          <Captcha :beforeStart="beforeStart" @success="onCaptchaSuccess" />
        </template>
      </my-input>
    </el-form-item>
  </el-form>
  <div>
    <my-button
      type="primary"
      class="w-full"
      @click="onSubmit"
      :loading="uploading"
      >确定</my-button
    >
  </div>
</template>
