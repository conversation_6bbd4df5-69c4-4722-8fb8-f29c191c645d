<template>
  <button
    :class="buttonVariants(props)"
    @click="
      () => {
        if (disabled) return
        emits('click')
      }
    "
  >
    <MyIcon v-if="icon" :icon="icon" :size="20" class="mr-2" />
    <slot />
  </button>
</template>

<script setup lang="ts">
import { cva, type VariantProps } from 'class-variance-authority'

const buttonVariants = cva(
  'font-semibold flex items-center justify-center outline-none transition-all',
  {
    variants: {
      type: {
        primary: 'bg-linear-primary text-primary_on-brand',
        default: 'border-[2px] border-tertiary text-primary_on-brand',
        form: 'border-box border border-tertiary text-tertiary',
      },
      size: {
        lg: 'px-6 h-14 text-base min-w-[196px]',
        md: 'px-4 h-12 text-base min-w-[136px]',
        sm: 'px-4 h-7 text-xs min-w-[80px]',
      },
      rounded: {
        true: 'rounded-full',
        false: 'rounded-lg',
      },
      loading: {
        true: 'cursor-not-allowed opacity-50',
      },
      disabled: {
        true: 'cursor-not-allowed opacity-50',
      },
      block: {
        true: 'w-full',
      },
    },
    compoundVariants: [
      {
        type: 'form',
        size: 'md',
        class: 'rounded-xl !h-10 w-full text-sm',
      },
    ],
    defaultVariants: {
      type: 'default',
      size: 'md',
      rounded: true,
      loading: false,
      disabled: false,
      block: false,
    },
  }
)

type ButtonProps = VariantProps<typeof buttonVariants>

const props = defineProps<{
  type?: ButtonProps['type']
  size?: ButtonProps['size']
  rounded?: ButtonProps['rounded']
  loading?: ButtonProps['loading']
  disabled?: ButtonProps['disabled']
  block?: ButtonProps['block']
  icon?: string
}>()

const emits = defineEmits(['click'])
</script>
