<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload v-model:value="styleImage" :drag="true" label="款式图" />
        <PictureUpload
          v-model:value="fabricImage"
          :drag="true"
          label="面料图"
        />
        <FormItem label="生成数量">
          <GenerateCount v-model:count="count" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { changeFabric } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, ref, watch } from 'vue'

const styleImage = ref<string | null | undefined>()
const fabricImage = ref<string | null | undefined>()
const count = ref<number>(4)
const confirmLoading = ref(false)
const activeIndex = ref(0)

const artwork = useArtwork()
const userStore = useUserInfoStore()

const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!styleImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传款式图')
    return
  }
  if (!fabricImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传面料图')
    return
  }
  confirmLoading.value = true
  try {
    const res = await changeFabric({
      num: count.value,
      drawParam: {
        styleImageUrl: styleImage.value,
        fabricImageUrl: fabricImage.value,
      },
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    ElMessage.error(error.message || '出错了')
  }
}

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    !!params.id && artwork.setCreatingArtworks([params])
    styleImage.value = params?.fabricReplaceFields?.styleImageUrl
    fabricImage.value = params?.fabricReplaceFields?.fabricImageUrl
  },
  { deep: true, immediate: true }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss"></style>
