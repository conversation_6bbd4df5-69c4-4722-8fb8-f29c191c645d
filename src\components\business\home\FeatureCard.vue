<template>
  <div class="feature-card group">
    <template v-if="cover.type === 'diff'">
      <ImageDiff
        :left-image="cover.before!"
        :right-image="cover.after!"
        class="feature-card-cover"
      />
    </template>
    <template v-else>
      <img :src="cover.src" class="feature-card-cover" />
    </template>
    <div class="relative flex-1 pb-[42px]">
      <div class="feature-card-title">{{ title }}</div>
      <div class="feature-card-desc">{{ desc }}</div>
      <div
        class="flex items-center justify-center absolute inset-0 translate-y-full group-hover:translate-y-0 transition-all duration-300 ease-in-out bg-white"
      >
        <GradientButton @click="router.push(path)">立即体验</GradientButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

interface DiffCover {
  type: 'diff'
  before: string
  after: string
}

interface ImageCover {
  type: 'image'
  src: string
}

interface Props {
  cover: DiffCover | ImageCover
  title: string
  desc: string
  path: string
}

defineProps<Props>()

const router = useRouter()
</script>
<style scoped>
.feature-card {
  @apply w-[474px] rounded-[32px] overflow-hidden flex-shrink-0 bg-white flex flex-col;
  box-shadow: 0px 4px 47.9px 0px rgba(19, 35, 68, 0.05);

  .feature-card-cover {
    @apply h-[315px];
  }

  .feature-card-title {
    @apply text-[24px] font-semibold text-primary text-center mt-[58px];
  }

  .feature-card-desc {
    @apply text-[16px] text-secondary text-center mt-4 px-[57px] h-12;
  }
}
</style>
