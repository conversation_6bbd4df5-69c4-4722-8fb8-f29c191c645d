<template>
  <div
    class="relative w-full"
    :class="[
      showStops ? 'my-slider-wrap my-slider-wrap-stops' : 'my-slider-wrap ',
    ]"
  >
    <el-slider
      v-model="sliderValue"
      :show-stops="showStops"
      :min="min"
      :max="max"
      :step="step"
      v-bind="restProps"
      tooltip-class="my-slider-tooltip"
    />
    <!-- 自己实现点位 -->
    <div
      v-if="showStops"
      class="my-slider-stops absolute w-full left-0 top-1/2"
    >
      <div
        class="my-slider-stop border-2 border-solid border-brand bg-white rounded-full absolute -translate-y-1/2 top-1/2"
        :style="{
          left: '-5px',
          width: `10px`,
          height: `10px`,
          display:
            (sliderValue as number) <= (min as number) ? 'block' : 'none',
        }"
        @click="$emit('update:modelValue', min as number)"
      ></div>
      <div
        class="my-slider-stop border-2 border-solid bg-white rounded-full absolute -translate-y-1/2 top-1/2"
        :class="[
          (sliderValue as number) === (min as number) + i * (step as number)
            ? 'border-brand'
            : 'border-neutral-200',
        ]"
        v-for="i in range"
        :key="i"
        :style="{
          left: `calc(${(1 / range) * i * 100}% - ${(i * 2 + 10) / 2}px)`,
          width: `${i * 2 + 10}px`,
          height: `${i * 2 + 10}px`,
          display:
            (sliderValue as number) <= (min as number) + i * (step as number)
              ? 'block'
              : 'none',
        }"
        @click="
          $emit('update:modelValue', (min as number) + i * (step as number))
        "
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { SliderProps } from 'element-plus'
import { computed } from 'vue'

interface MySliderProps extends Partial<SliderProps> {}

const { modelValue, showStops, min, max, step, ...restProps } =
  defineProps<MySliderProps>()

const range = computed(() => {
  return Math.floor(((max as number) - (min as number)) / (step as number))
})

const emits = defineEmits(['update:modelValue', 'change'])

const sliderValue = computed({
  get: () => {
    return modelValue
  },
  set: val => {
    emits('update:modelValue', val)
    emits('change', val)
  },
})
</script>

<style lang="postcss" >
.my-slider-tooltip.el-popper {
  box-shadow: 0px 2px 15.8px 0px rgba(0, 0, 0, 0.25);
  @apply min-w-7 text-center text-neutral-950 bg-white border border-solid font-normal border-white px-1.5 py-0 rounded;
}
</style>
<style scoped lang="scss">
.my-slider-wrap.my-slider-wrap-stops {
  :deep(.el-slider__button) {
    opacity: 0;
  }
}
.el-slider {
  height: 12px;
  --el-slider-height: 8px;
  --el-slider-border-radius: 12px;
  --el-slider-runway-bg-color: var(--neutral-100);
  --el-slider-button-size: 16px;
  --el-slider-button-wrapper-size: 25px;
  --el-slider-button-wrapper-offset: -9px;
  :deep(.el-slider__runway.show-input) {
    margin-right: 12px;
  }
  :deep(.el-slider__bar) {
    @apply bg-linear-primary;
  }
  :deep(.el-slider__button) {
    @apply border-brand bg-neutral-50 !scale-100;
  }
  :deep(.el-slider__stop) {
    // 隐藏自带的点，自己实现组件放大的点
    @apply hidden;
  }
  :deep(.el-input-number) {
    width: 64px;
    height: 40px;
    .el-input__wrapper {
      @apply border border-tertiary transition-all duration-300 shadow-none bg-transparent rounded-xl;

      &:hover,
      &:focus-within {
        @apply border-brand;
      }

      .el-input__inner {
        @apply text-primary text-sm;
      }
    }
  }
}
</style>
