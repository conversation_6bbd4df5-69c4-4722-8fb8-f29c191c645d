// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** Admin Login Admin login to the system with username and verification code. POST /api/v1/user/admin/login */
export async function adminLogin(
  body: API.LoginRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString>('/api/v1/user/admin/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** Logout Logout the currently authenticated admin user from the system. POST /api/v1/user/admin/logout */
export async function adminLogout(options?: { [key: string]: any }) {
  return request<API.ResultBoolean>('/api/v1/user/admin/logout', {
    method: 'POST',
    ...(options || {}),
  })
}

/** View User Profile Allows the user to view their profile information, including personal details, etc. GET /api/v1/user/admin/profile */
export async function adminUserProfile(options?: { [key: string]: any }) {
  return request<API.ResultUserResponseVO>('/api/v1/user/admin/profile', {
    method: 'GET',
    ...(options || {}),
  })
}

/** Send verification code Sends a verification code to the provided phone number or email address. POST /api/v1/user/admin/send_verification_code */
export async function adminSendVerificationCode(
  body: API.VerificationCodeSendRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean>(
    '/api/v1/user/admin/send_verification_code',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}

/** Update User Settings Allows an admin to update user settings, including role and security authentication. PUT /api/v1/user/admin/update_settings */
export async function updateUserSettings(
  body: API.UserSettingsUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean>('/api/v1/user/admin/update_settings', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** Get User List Fetch a list of users with optional filters such as username, phone number, etc. POST /api/v1/user/admin/users */
export async function getUsers(
  body: API.UserQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultListUserResponseVO>('/api/v1/user/admin/users', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** Get User List (Paginated) Fetch a paginated list of users with optional filters such as username, phone number, etc. POST /api/v1/user/admin/users_page */
export async function getUserPage(
  body: API.UserQueryPageRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageResponseUserResponseVO>(
    '/api/v1/user/admin/users_page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}
