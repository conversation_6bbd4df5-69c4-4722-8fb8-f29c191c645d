// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** 此处后端没有提供注释 POST /add_task_model */
export async function addTaskModel(
  body: API.TaskModelInfoVo,
  options?: { [key: string]: any }
) {
  return request<any>('/add_task_model', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 GET /statistic/modelGenAvgTime */
export async function modelGenAvgTime(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.modelGenAvgTimeParams,
  options?: { [key: string]: any }
) {
  return request<number>('/statistic/modelGenAvgTime', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}
