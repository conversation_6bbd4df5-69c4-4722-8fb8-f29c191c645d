<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <FormItem label="参考模式">
          <div class="grid grid-cols-2 gap-2">
            <div
              class="mode"
              :class="{ selected: referenceModel === 'STYLE' }"
              @click="referenceModel = 'STYLE'"
            >
              风格参考
            </div>
            <div
              class="mode"
              :class="{ selected: referenceModel === 'CONTENT' }"
              @click="referenceModel = 'CONTENT'"
            >
              内容参考
            </div>
          </div>
        </FormItem>
        <FormItem label="画面描述">
          <MyTextarea
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount v-model:count="count" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { fabricPatternCreation } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, ref, watch } from 'vue'

const uploadImage = ref<string | null | undefined>()
const referenceModel = ref<'STYLE' | 'CONTENT'>('STYLE')
const prompt = ref('')
const count = ref<number>(4)
const confirmLoading = ref(false)
const activeIndex = ref(0)

const artwork = useArtwork()
const userStore = useUserInfoStore()

const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }
  try {
    confirmLoading.value = true
    const res = await fabricPatternCreation({
      num: count.value,
      drawParam: {
        initImage: uploadImage.value,
        prompt: prompt.value,
        referenceModel: referenceModel.value,
      },
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    ElMessage.error(error.message || '出错了')
  }
}

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    !!params.id && artwork.setCreatingArtworks([params])
    uploadImage.value = params?.fabricPatternCreationFields?.initImage
    referenceModel.value =
      params?.fabricPatternCreationFields?.referenceModel ?? 'STYLE'
    prompt.value = params?.fabricPatternCreationFields?.prompt ?? ''
  },
  { deep: true, immediate: true }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss">
.mode {
  @apply text-primary font-semibold text-sm rounded-xl h-12 cursor-pointer border border-secondary border-solid flex items-center justify-center;

  &.selected {
    @apply border-brand text-brand;
  }
}
</style>
