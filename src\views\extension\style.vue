<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          label="款式图片"
        />
        <PictureUpload
          v-model:value="uploadImage1"
          :drag="true"
          label="灵感图片"
          @onUrlChange="onUrlChangeFun1"
        />
        <el-button
          @click="editImg"
          style="border-radius: 0.5rem"
          v-if="uploadImage1"
          >编辑重绘区域</el-button
        >
        <FormItem label="画面描述">
          <MyTextarea
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <FormItem label="参考强度">
          <MySlider
            :max="1"
            :min="0"
            v-model="sliderStep"
            show-input
            :step="0.01"
          />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
  <PaintModal
    v-model:visible="paintVisible"
    :bottomImage="uploadImage1"
    @confirm="sureMask"
    :maskImage="maskImage || undefined"
  />
</template>
<script setup lang="ts">
import { styleTransfer } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, ref, watch, onMounted } from 'vue'
import { toBlob } from '@/utils'
import { useImgwork } from '@/store'
const useImg = useImgwork()
const uploadImage = ref()
const uploadImage1 = ref()
const prompt = ref('')
const confirmLoading = ref(false)
const activeIndex = ref(0)
const maskImage = ref()
const maskOne = ref()
const originImg = ref()
const originImg1 = ref()
const artwork = useArtwork()
const sliderStep = ref(0.2)
const userStore = useUserInfoStore()
const paintVisible = ref(false)
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()
const editImg = async () => {
  paintVisible.value = true
}
const sureMask = (e: { url: string }) => {
  maskImage.value = e.url
}
const onUrlChangeFun1 = () => {
  maskImage.value = null
  maskOne.value = null
}
const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传款式图片')
    return
  }
  if (!uploadImage1.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传灵感图片')
    return
  }
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }
  try {
    confirmLoading.value = true
    const response = await toBlob(uploadImage.value)
    originImg.value = response
    const response1 = await toBlob(uploadImage1.value)
    originImg1.value = response1
    let formData = new FormData()
    formData.append('image_clothes', originImg.value)
    formData.append('image_clothes_url', uploadImage.value)
    formData.append('image_ref', originImg1.value)
    formData.append('image_ref_url', uploadImage1.value)
    if (maskImage.value) {
      const maskResponse = await toBlob(maskImage.value)
      maskOne.value = maskResponse
      formData.append('image_mask', maskOne.value)
      formData.append('image_mask_url', maskImage.value)
    }
    formData.append('prompt', prompt.value)
    formData.append('similar', String(sliderStep.value))
    formData.append('n_iter', '1')
    const res = await styleTransfer(formData)
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.change_head_plusFields?.imageClothesUrl
    uploadImage1.value = params?.change_head_plusFields?.imageRefUrl
    maskImage.value = params?.change_head_plusFields?.imageMaskUrl
    prompt.value = params?.change_head_plusFields?.prompt || ''
    sliderStep.value = params?.change_head_plusFields?.similar2
  }
)
onMounted(async () => {
  uploadImage.value = useImg.imgUrlStyle
})
onBeforeUnmount(() => {
  stopCreatingPoll()
  useImg.setUpdateUrlStyle('')
})
</script>
<style scoped lang="scss">
:deep(.el-button:hover) {
  color: #606022;
  background: none;
  border-color: #dcdfe6;
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: #fbdef5;
  border: 1px solid #fbdef5;
  color: #fbdef5;
}

::v-deep .el-checkbox__label {
  color: #dcdfe6;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #fbdef5 !important;
}

::v-deep .el-checkbox__inner:hover {
  border-color: #dcdfe6;
}
</style>
  