<script setup lang="ts">
const value = defineModel<boolean>()
// const goAgreement = (key: string) => {
//   const href = `${window.location.origin}/agreement?key=${key}`
//   window.open(href, '__blank')
// }
const goAgreement = () => {
  window.open('https://www.yuque.com/shuangxi-m9ar4/pmk17h?#', '__blank')
}
</script>
<template>
  <div class="mb-6">
    <!-- <el-radio :value="true" class="login-radio" v-model="value">
      我已阅读并同意<a @click.stop="goAgreement('userAgreement')"
        >《服务条款》</a
      >
    </el-radio> -->
    <el-radio :value="true" class="login-radio" v-model="value">
      我已阅读并同意<a @click.stop="goAgreement()">《服务条款》</a>
    </el-radio>
  </div>
</template>
<style scoped lang="scss">
.login-radio {
  height: 14px;
  :deep(.el-radio__label) {
    @apply text-xs text-neutral-500 leading-[14px] font-semibold;
  }
  :deep(.el-radio__input.is-checked .el-radio__inner) {
    background: var(--primary);
    border-color: var(--primary);
  }
  a {
    @apply text-neutral-900;
  }
}
</style>
