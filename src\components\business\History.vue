<template>
  <div class="h-full -mx-5">
    <div class="h-full">
      <h4
        class="text-[28px] font-semibold text-primary flex items-center justify-between px-6"
      >
        历史记录

        <div
          class="border-2 border-neutral-300 text-neutral-900 rounded-full w-10 h-10 flex justify-center items-center ml-6 cursor-pointer"
          @click="$emit('close')"
        >
          <MyIcon icon="close" :size="20" />
        </div>
      </h4>
      <div class="grid grid-cols-4 gap-2 mt-3 py-3 flex-1 px-6 overflow-auto">
        <div
          v-for="image in newData"
          :key="image.id"
          class="rounded-xl w-[104px] h-[104px] bg-neutral-50 cursor-pointer overflow-hidden"
          @click="debounceSelectPic(image)"
        >
          <MyImage
            v-if="image.status === 'SUCCESS' && !!image.result?.url"
            :src="image.result?.thumbUrl ?? image.result?.url"
            class="w-full h-full flex items-center justify-center"
            tag="v400"
          />
          <div
            class="flex items-center justify-center w-full h-full px-2"
            v-else-if="image.status === 'GENERATING'"
          >
            <el-progress
              :percentage="(image?.completePercent ?? 0) * 100"
              :stroke-width="4"
              :show-text="false"
              color="var(--primary)"
              class="w-full"
            />
          </div>
          <div v-else class="flex items-center justify-center w-full h-full">
            <MyIcon icon="Warning" :size="20" class="text-neutral-400" />
          </div>
        </div>
      </div>
      <div class="flex justify-between items-center px-6 pt-6">
        <div class="text-secondary text-sm">共 {{ total }} 项数据</div>
        <el-pagination
          layout="prev, pager, next"
          :total="total"
          :page-size="PAGE_SIZE"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PAGE_SIZE } from '@/config'
import { useClickPreviewArtwork } from '@/hooks/useClickPreviewArtwork'
import { useHistoryList } from '@/hooks/useHistoryList'
import { useUpdateHistoryList } from '@/hooks/useUpdateHistoryList'
import { debounce, merge } from 'lodash'
import { computed, onMounted } from 'vue'
import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import { xhrDownload } from '@/utils/file'
defineEmits(['close'])

const { drawType } = defineProps<{
  drawType: number
}>()

const { data, fetchData, total } = useHistoryList()
const { handleSelectPic } = useClickPreviewArtwork()

const debounceSelectPic = debounce(
  (image: API.TaskUnitVO) => handleSelectPic(image),
  500
)

// 轮询，更新进度
const progressCache = useUpdateHistoryList({
  list: data,
})
const handleDownload = () => {
  console.log(newData.value, '584')

  const zip = new JSZip()
  newData.value.forEach((image, index) => {
    if (image.result?.url) {
      setTimeout(() => {
        xhrDownload(image.result.url)
      }, index * 500) // 每隔500ms下载一张，避免浏览器同时发起太多请求
    }
  })
  // for (let i = 0; i < newData.value.length; i++) {
  //   // 获取图片资源
  //   fetch(newData.value[i].result?.url!)
  //     .then(response => response.blob())
  //     .then(blob => {
  //       // 将Blob添加到ZIP包中，这里假设图片名为'image'+i+'.jpg'
  //       zip.file(`img${i + 1}.png`, blob)
  //       // 在所有图片都处理完之后生成ZIP
  //       if (i === newData.value.length - 1) {
  //         zip.generateAsync({ type: 'blob' }).then(content => {
  //           // 下载ZIP文件
  //           saveAs(content, 'downloadedimages.zip')
  //         })
  //       }
  //     })
  // }
}
const newData = computed(() => {
  return data?.value?.map(item => {
    if (progressCache.value[item.id!]) {
      return merge({}, item, progressCache.value[item.id!])
    }
    return item
  })
})

const handleCurrentChange = (page: number) => {
  fetchData({ drawType: drawType!, page: page - 1 })
}

onMounted(() => {
  fetchData({ drawType: drawType!, page: 0 })
})
</script>

<style></style>
