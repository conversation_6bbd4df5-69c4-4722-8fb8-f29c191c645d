<script setup lang="ts">
interface IProps {
  icon: string
  size?: number
  color?: string
}
const { icon, size = 24 } = defineProps<IProps>()
</script>

<template>
  <i
    class="iconfont leading-none inline-block"
    :class="[`icon-${icon}`]"
    :style="{
      width: `${size}px`,
      height: `${size}px`,
      fontSize: `${size}px`,
      color: color
        ? color.startsWith('--')
          ? `var(${color})`
          : `${color}`
        : undefined,
    }"
  ></i>
</template>

<style scoped lang="scss"></style>
