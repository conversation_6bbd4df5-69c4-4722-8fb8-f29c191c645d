<script setup lang="ts">
import Autoscroll from '@/components/ui/Autoscroll.vue'
import { useSwiper } from 'swiper/vue'

interface ImageViewerProps {
  previewImages: Array<{ url: string }>
  activeIndex?: number
}

const { previewImages, activeIndex = 0 } = defineProps<ImageViewerProps>()

const swiper = useSwiper()
</script>
<template>
  <Autoscroll :activeIndex="activeIndex" :gutter="24" class="my-6 -mx-3">
    <div class="flex justify-start items-center mx-auto">
      <div
        v-for="(it, i) in previewImages"
        :key="i"
        class="navigation flex-shrink-0"
        :class="{ active: i === activeIndex }"
        @click="
          () => {
            swiper.slideTo(i)
          }
        "
      >
        <div
          class="flex items-center justify-center w-full h-full px-2 rounded-lg"
          v-if="it.status === 'GENERATING'"
        >
          <el-progress
            :percentage="(it?.completePercent ?? 0) * 100"
            :stroke-width="4"
            :show-text="false"
            color="var(--primary)"
            class="w-full"
          />
        </div>
        <MyImage
          :src="it.result?.thumbUrl ?? it.result?.url"
          tag="v400"
          :lazy="false"
          class="rounded overflow-hidden w-full h-full flex items-center justify-center"
        />
      </div>
    </div>
  </Autoscroll>
</template>
<style scoped lang="scss">
.navigation {
  @apply relative rounded-lg bg-white w-[105px] h-[105px] mx-3 overflow-hidden cursor-pointer border border-white border-solid;
  &.active {
    @apply border-brand;
  }
}
</style>
