<template>
  <div class="overflow-hidden pb-16">
    <div class="flex w-max gap-16 move">
      <slot />
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped>
@keyframes move {
  from {
    transform: translateX(0);
  }

  to {
    /* 补偿 gap-16 (4rem) 的距离 */
    transform: translateX(calc(-50% - 2rem));
  }
}

.move {
  animation: move 30s linear infinite;
  @apply transform-gpu;
}

.move:hover {
  animation-play-state: paused;
}
</style>
