<script setup lang="ts">
import { useScroll } from '@/hooks/useScroll'
import { watch } from 'vue'

interface ScrollProps {
  /** 选中的tabindex */
  activeIndex: number
  /** 每一个item的距离，保证按钮切换时的精度 */
  gutter?: number
  /** 滑动方向 */
  direction?: 'vertical' | 'horizontal'
  /** 距离系数，默认滑动半个容器 */
  distanceCoefficient?: number
}
const {
  activeIndex,
  gutter = 12,
  direction = 'horizontal',
  distanceCoefficient = 0.5,
} = defineProps<ScrollProps>()

const [{ scrollLeft, scrollTop }, scrollApi, scrollContainer] = useScroll({
  tension: 300,
  duration: 600,
})

const clamp = (
  position: number,
  min: number | undefined,
  max: number | undefined
) => {
  let ret = position
  if (min !== undefined) {
    ret = Math.max(position, min)
  }
  if (max !== undefined) {
    ret = Math.min(ret, max)
  }
  return ret
}

const animateHorizontal = (direction?: 'LEFT' | 'RIGHT') => {
  const container = scrollContainer.value
  if (!container) return
  const tabWrapper = container.children.item(0) as HTMLDivElement
  if (!tabWrapper) return
  const activeTab = tabWrapper.children.item(activeIndex) as HTMLDivElement
  if (!activeTab) return
  /**选中tab的offsetLeft */
  const activeTabLeft = activeTab.offsetLeft
  /**选中tab的宽度 */
  const activeTabWidth = activeTab.offsetWidth
  /**容器有效宽度 */
  const containerWidth = container.offsetWidth
  /**容器总宽度（含超出部分） */
  const containerScrollWidth = container.scrollWidth
  /**容器scroll的距离 */
  const containerScrollLeft = container.scrollLeft
  /**最大可以滚动的距离 = overflow超出部分 */
  const maxScrollDistance = containerScrollWidth - containerWidth

  const oneceDistance = Math.round(
    (containerWidth + gutter) * distanceCoefficient
  )
  if (maxScrollDistance <= 0) return
  if (direction === 'LEFT') {
    const nextScroll = clamp(
      containerScrollLeft - oneceDistance,
      0,
      maxScrollDistance
    )
    scrollApi.scrollTo(nextScroll, scrollTop)
  } else if (direction === 'RIGHT') {
    const nextScroll = clamp(
      containerScrollLeft + oneceDistance,
      0,
      maxScrollDistance
    )
    scrollApi.scrollTo(nextScroll, scrollTop)
  } else {
    const nextScroll = clamp(
      activeTabLeft - (containerWidth - activeTabWidth) / 2,
      0,
      maxScrollDistance
    )
    scrollApi.scrollTo(nextScroll, scrollTop)
  }
}

const animateVertical = (direction?: 'UP' | 'DOWN') => {
  const container = scrollContainer.value
  if (!container) return
  const tabWrapper = container.children.item(0) as HTMLDivElement
  if (!tabWrapper) return
  const activeTab = tabWrapper.children.item(activeIndex) as HTMLDivElement
  if (!activeTab) return
  /**选中tab的offsetLeft */
  const activeTabTop = activeTab.offsetTop
  /**选中tab的宽度 */
  const activeTabHeight = activeTab.offsetHeight
  /**容器有效宽度 */
  const containerHeight = container.offsetHeight
  /**容器总宽度（含超出部分） */
  const containerScrollHeight = container.scrollHeight
  /**容器scroll的距离 */
  const containerScrollTop = container.scrollTop
  /**最大可以滚动的距离 = overflow超出部分 */
  const maxScrollDistance = containerScrollHeight - containerHeight
  const oneceDistance = Math.round(
    (containerHeight + gutter) * distanceCoefficient
  )
  if (maxScrollDistance <= 0) return
  if (direction === 'UP') {
    const nextScroll = clamp(
      containerScrollTop - oneceDistance,
      0,
      maxScrollDistance
    )
    scrollApi.scrollTo(scrollLeft, nextScroll)
  } else if (direction === 'DOWN') {
    const nextScroll = clamp(
      containerScrollTop + oneceDistance,
      0,
      maxScrollDistance
    )
    scrollApi.scrollTo(scrollLeft, nextScroll)
  } else {
    const nextScroll = clamp(
      activeTabTop - (containerHeight - activeTabHeight) / 2,
      0,
      maxScrollDistance
    )
    scrollApi.scrollTo(scrollLeft, nextScroll)
  }
}

watch(
  () => activeIndex,
  () => {
    if (activeIndex >= 0) {
      if (direction === 'horizontal') {
        animateHorizontal()
      } else {
        animateVertical()
      }
    }
  }
)
</script>

<template>
  <div class="autoScrollWrap" :class="{ 'h-full': direction === 'vertical' }">
    <div class="autoScroll" ref="scrollContainer">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="scss">
.autoScroll {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  overflow: auto;
  height: 100%;
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
}
</style>
