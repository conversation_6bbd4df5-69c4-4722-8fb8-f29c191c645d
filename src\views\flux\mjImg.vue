<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full" style="position: relative">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <FormItem label="画面描述">
          <MyTextarea1
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <FormItem label="重绘强度">
          <MySlider
            :max="1"
            :min="0"
            v-model="sliderRedraw"
            show-input
            :step="0.01"
          />
        </FormItem>
        <FormItem label="迭代步数">
          <MySlider :max="60" :min="1" v-model="sliderStep" show-input />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
        <!-- <FormItem label="Image Width">
              <MySlider :max="1912" :min="512" v-model="sliderWidth" show-input />
            </FormItem>
            <FormItem label="Image Height">
              <MySlider :max="1912" :min="512" v-model="sliderHeight" show-input />
            </FormItem>
            <FormItem label="Iteration Steps">
              <MySlider :max="60" :min="1" v-model="sliderStep" show-input />
            </FormItem>
            <FormItem label="High-Definition Redraw">
              <GenerateCount
                :counts="radioList"
                :count="radioCount"
                @onChangeSelect="selectChange1"
              />
            </FormItem>
            <FormItem label="Number of Generations">
              <GenerateCount :count="count" @onChangeSelect="selectChange" />
            </FormItem> -->
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
      <script setup lang="ts">
import { createTaskMj2 } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { ElMessage } from 'element-plus'
import { onBeforeUnmount, ref, watch } from 'vue'
const prompt = ref('')
const uploadImage = ref<string | null | undefined>()
const confirmLoading = ref(false)
const activeIndex = ref(0)
const sliderRedraw = ref(1)
const loading = ref('')
const artwork = useArtwork()
const userStore = useUserInfoStore()
const sliderHeight = ref(1024)
const sliderWidth = ref(768)
const sliderStep = ref(25)
const count = ref(4)
const radioList = ref(['no', 'yes'])
const radioCount = ref('no')
const imgAll = ref([])
const imgID = ref()
const originImg = ref()
const imgSize1 = ref(false)
const num = ref(0)
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()
const selectChange = e => {
  count.value = e
}
const selectChange1 = e => {
  radioCount.value = e
}
const handleConfirm = async () => {
  console.log(count.value)
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }
  if (prompt.value == '提示词生成中...' || prompt.value == '提示词润色中...') {
    ElMessage.closeAll()
    ElMessage.error('请等待提示词生成完成')
    return
  }
  try {
    confirmLoading.value = true
    const res = await createTaskMj2({
      prompt: prompt.value,
      img_url: uploadImage.value,
    })
    confirmLoading.value = false
    activeIndex.value = 0
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    ElMessage.error(error.message || '出错了')
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    prompt.value = params?.change_head_plusFields?.prompt || ''
    uploadImage.value = params?.change_head_plusFields?.imageUrl
  }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
  <style scoped lang="scss"></style>
      