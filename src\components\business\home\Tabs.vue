<template>
  <div
    class="flex items-center justify-center gap-10 w-max p-1 border border-tertiary rounded-full"
  >
    <div
      v-for="(item, index) in props.items"
      :key="index"
      class="flex items-center justify-center gap-2.5 py-4 px-[45px] cursor-pointer rounded-full"
      :class="{ active: index === props.active }"
      @click="handleClick(index)"
    >
      <MyIcon :icon="item.icon" v-if="item.icon" />
      <div class="text-[18px] font-semibold">{{ item.title }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    items: {
      icon?: string
      title: string
    }[]
    active: number
  }>(),
  {
    active: 0,
  }
)

const emit = defineEmits(['update:active'])

const handleClick = (index: number) => {
  console.log(index)
  emit('update:active', index)
}
</script>

<style scoped>
.active {
  background: linear-gradient(90deg, #faddf1 0%, #f7cef2 52.61%, #b5d6fd 100%);
}
</style>
