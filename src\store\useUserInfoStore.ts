import { defineStore } from 'pinia'
import { viewUserProfile } from '@/clients/api/base'
import { authLogout } from '@/clients/api/authentication'
import { getAuth, removeAuth } from '@/utils'
import { getAccountBalance } from '@/clients/api/account'

const defaultBaseInfo = {
  nickname: '',
  avatar: '',
  phone: '',
  email: '',
  uid: 0,
  roleType: 'USER',
  drawingTimes: 0,
  passwordInitialized: false,
}
export const useUserInfoStore = defineStore('userinfoStore', {
  state: () => ({
    logined: false,
    base: {
      ...defaultBaseInfo,
    },
    // 设置登录类型，有值的时候显示弹窗，值为默认登录类型
    loginType: '',
    // 修改的类型 -- 设置密码、设置邮箱等
    settingLoginType: '' as any,
    // 编辑用户信息
    updateProfileVisible: false,
    // 安全验证
    validateAccountInfo: null as any,
    // 忘记密码 手机号
    forgotPwdPhone: '',
  }),
  actions: {
    getUserInfo() {
      if (!getAuth()) {
        return
      }
      // 获取用户信息
      viewUserProfile()
        .then(resp => {
          const data = resp?.data
          if (!data) {
            return
          }
          this.logined = true
          this.base = data as any
        })
        .catch(() => {
          // ElMessage.error()
        })
    },
    /** 登录弹窗 */
    setLoginType(loginType: string) {
      this.loginType = loginType
    },
    /** 设置密码/设置邮箱等 */
    setSettingLoginType(type: string) {
      this.settingLoginType = type
    },
    /** 登出 */
    logout() {
      authLogout().then(resp => {
        if (resp?.data) {
          this.logined = false
          this.base = {
            ...defaultBaseInfo,
          }
          ElMessage.success('已退出登录')
          removeAuth()
          window.location.reload()
        }
      })
    },
    /** 编辑个人信息 */
    setUpdateProfileVisible(visible: boolean) {
      this.updateProfileVisible = visible
    },
    /** 安全认证 */
    setValidateAccountInfo(info: any) {
      this.validateAccountInfo = info
    },
    /** 忘记密码 */
    setForgotPwdPhone(phone: string) {
      this.forgotPwdPhone = phone
    },
    /** 作画次数 */
    updateDrawingTimes() {
      getAccountBalance().then(resp => {
        this.base.drawingTimes = resp?.data || 0
      })
    },
  },
})
