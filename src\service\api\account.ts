// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** Get Account Balance Get the account balance of the currently authenticated user. GET /api/v1/user/account/times_account_balance */
export async function getAccountBalance(options?: { [key: string]: any }) {
  return request<API.ResultLong>('/api/v1/user/account/times_account_balance', {
    method: 'GET',
    ...(options || {}),
  })
}

/** Times Account Reward Reward times to user account. POST /api/v1/user/account/times_account_reward */
export async function timesAccountReward(
  body: API.AccountTimesRewardRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong>('/api/v1/user/account/times_account_reward', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** Page Times Account Reward Page the times account reward records. GET /api/v1/user/account/times_reward_record_page */
export async function pageTimesAccountReward(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageTimesAccountRewardParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageResponseRewardTimesRecordVO>(
    '/api/v1/user/account/times_reward_record_page',
    {
      method: 'GET',
      params: {
        ...params,
        request: undefined,
        ...params['request'],
      },
      ...(options || {}),
    }
  )
}
