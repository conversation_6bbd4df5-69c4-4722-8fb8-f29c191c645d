import dayjs from 'dayjs'

/** 获取文件后缀 */
export const getFileExtension = (url: string) => {
  return url.split('.').pop() ?? ''
}

/** svg文件 */
export const isSvg = (url: string) => {
  return getFileExtension(url) === 'svg'
}

/** 是否能转换成webp格式 */
export const canConvertWebp = (url: string) => {
  return ['png', 'jpeg', 'jpg', 'gif'].includes(getFileExtension(url))
}

/** 缩略图： 支持情况下转，否则原图 */
const toViewImage = (url: string, width: number) => {
  return canConvertWebp(url)
    ? `${url}?imageView2/2/w/${width}/q/90/format/webp`
    : url
}
/** 转为文件流 */
export const toBlob = async (url: string) => {
  try {
    const response = await fetch(url);
    const blob = await response.blob();
    return blob;
  } catch (error) {
    return null;
  }
}
/** base64转为文件流 */
export const base64ToBinary = async (base64String: string) => {
  const base64Data = base64String.split(',')[1] || base64String;

  // 解码Base64字符串
  const binaryString = atob(base64Data);
  const bytes = new Uint8Array(binaryString.length);

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // 创建Blob
  const blob = new Blob([bytes]);

  return blob;
}
/** url转base64 */
export const urlToBase64 = async (url: string) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'Anonymous'
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)
      const dataURL = canvas.toDataURL('image/jpeg')
      resolve(dataURL)
    }
    img.onerror = e => {
      reject(e)
    }
    img.src = `${url}?x-oss-process=image/resize,w_800`
  })
}
export const to400Image = (url: string) => {
  return toViewImage(url, 400)
}

export const to800Image = (url: string) => {
  return toViewImage(url, 800)
}

export const to1200Image = (url: string) => {
  return toViewImage(url, 1200)
}

export const to1440Image = (url: string) => {
  return toViewImage(url, 1440)
}

export const showMobile = (args: number | string | undefined | null) => {
  return args?.toString().replace(/^(.{3})(?:\w+)(.{4})$/, '$1****$2')
}

export const showEmail = (args: string | undefined | null) => {
  return args?.replace(/^(.{3})(?:.+)(.{0})$/, '$1******$2')
}

export const toYMDHms = (timestrap: number) => {
  return dayjs(timestrap).format('YYYY-MM-DD HH:mm:ss')
}
