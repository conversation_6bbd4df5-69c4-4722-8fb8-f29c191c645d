<script setup lang="ts">
import { getGeneralInformation } from '@/clients/api/resource'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const html = ref('')
onMounted(() => {
  const key = (route.query.key || 'userAgreement') as string
  getGeneralInformation().then(resp => {
    html.value = (resp.data as any)?.[key] || ''
  })
})
</script>

<template>
  <div v-html="html" class="p-4"></div>
</template>

<style scoped lang="scss"></style>
