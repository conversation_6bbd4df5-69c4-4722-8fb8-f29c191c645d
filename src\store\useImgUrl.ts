import { defineStore } from 'pinia'
/**
 * 
 *
 */

export const useImgwork = defineStore('imgWork', {
    state: () =>
        <
        {
            imgUrl: string,
            imgUrlCraft: string,
            imgUrlModelSwap: string,
            imgUrlModel: string,
            imgUrlStyle: string,
            imgUrlFabric: string,
            imgUrlImage: string,
            imgUrlResup: string,
        }
        >{
            imgUrl: '',
            imgUrlCraft: '',
            imgUrlModelSwap: '',
            imgUrlModel: '',
            imgUrlStyle: '',
            imgUrlFabric: '',
            imgUrlImage: '',
            imgUrlResup: '',
        },
    actions: {
        setUpdateUrl(url: string) {
            this.imgUrl = url
        },
        setUpdateUrlCraft(url: string) {
            this.imgUrlCraft = url
        },
        setUpdateUrlModelSwap(url: string) {
            this.imgUrlModelSwap = url
        },
        setUpdateUrlModel(url: string) {
            this.imgUrlModel = url
        },
        setUpdateUrlStyle(url: string) {
            this.imgUrlStyle = url
        },
        setUpdateUrlFabric(url: string) {
            this.imgUrlFabric = url
        },
        setUpdateUrlImage(url: string) {
            this.imgUrlImage = url
        },
        setUpdateUrlResup(url: string) {
            this.imgUrlResup = url
        },
    }
})
