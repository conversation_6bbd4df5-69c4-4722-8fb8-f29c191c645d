<template>
  <div class="my-textarea" :class="class">
    <el-input
      v-model="modelValue"
      type="textarea"
      resize="none"
      :maxlength="maxlength"
      v-bind="$attrs"
    />
    <div class="text-xs text-tertiary" v-if="showCount && maxlength">
      <span>{{ num }}</span
      >/{{ maxlength }}
    </div>
    <div class="absolute right-2 bottom-2">
      <MyIcon
        icon="copy"
        :size="16"
        class="cursor-pointer text-tertiary transition-all hover:text-brand"
        @click="copy(modelValue ?? '')"
      />
      <MyIcon
        icon="delete1"
        :size="16"
        class="cursor-pointer text-tertiary ml-2 transition-all hover:text-brand"
        @click="$emit('update:modelValue', '')"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import copy from 'copy-to-clipboard'
import { computed } from 'vue'

interface IStepInputProps {
  maxlength?: number
  showCount?: boolean
  class?: string
}
const { maxlength, showCount = false } = defineProps<IStepInputProps>()
const modelValue = defineModel<string>()

const num = computed(() => modelValue.value?.length || 0)

const emits = defineEmits(['update:modelValue'])
</script>
<style scoped lang="scss">
.my-textarea {
  @apply border border-tertiary rounded-xl relative;
  padding: 14px 8px 8px 16px;

  &:hover {
    border-color: #eaabe6;
  }
  :deep(.el-textarea) {
    .el-textarea__inner {
      height: 120px;
      padding: 0 8px 0 0;
      border: none;
      box-shadow: none;
      @apply text-xs font-semibold text-primary placeholder:text-placeholder !bg-transparent;
    }
  }
}
</style>
