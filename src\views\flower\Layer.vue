<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          @onUrlChange="getLayerSeparation"
        />
        <FormItem label="颜色层次">
          <StepInput
            v-model="layerCount!"
            :min="1"
            :max="32"
            :disabled="!uploadImage"
          />
        </FormItem>
      </div>
    </template>
    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
<script setup lang="ts">
import { layerSeparation, preLayerSeparation } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, ref, watch } from 'vue'

const uploadImage = ref<string | null | undefined>()
const layerCount = ref<number | null | undefined>(null)
const confirmLoading = ref(false)

const activeIndex = ref(0)

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

const artwork = useArtwork()
const userStore = useUserInfoStore()

const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  try {
    confirmLoading.value = true
    const res = await layerSeparation({
      num: 1,
      drawParam: {
        imageUrl: uploadImage.value,
        layerCount: layerCount.value ?? 1,
      },
    })
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    ElMessage.error(error.message || '出错了')
  }
}

const getLayerSeparation = async (url?: string) => {
  if (!url) return
  try {
    layerCount.value = null
    const res = await preLayerSeparation({
      imageUrl: url,
    })
    layerCount.value = res?.data?.layerCount!
  } catch (error) {
    ElMessage.error('提取图层失败')
  }
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    console.log(params, 'params')
    !!params.id && artwork.setCreatingArtworks([params])
    uploadImage.value = params?.layerSeparationFields?.inImgUrl
    layerCount.value = params?.layerSeparationFields?.numColors
    if (!params?.layerSeparationFields?.numColors) {
      getLayerSeparation(params?.layerSeparationFields?.inImgUrl)
    }
  },
  { deep: true, immediate: true }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss"></style>
