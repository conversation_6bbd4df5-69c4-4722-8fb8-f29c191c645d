// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** User login Logs in a user by processing their credentials and authentication device information. POST /api/v1/user/auth/login */
export async function authLogin(
  body: API.LoginRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString>('/api/v1/user/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** User logout Logs out the currently authenticated user. POST /api/v1/user/auth/logout */
export async function authLogout(options?: { [key: string]: any }) {
  return request<API.ResultBoolean>('/api/v1/user/auth/logout', {
    method: 'POST',
    ...(options || {}),
  })
}

/** Register a new user Registers a new user using phone number and verification code, along with client IP and device ID. POST /api/v1/user/auth/register */
export async function authRegister(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.authRegisterParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultString>('/api/v1/user/auth/register', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  })
}

/** Reset Email Resets the user's email address using phone verification and a new email address. POST /api/v1/user/auth/reset_email */
export async function authResetEmail(
  body: API.EmailResetRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean>('/api/v1/user/auth/reset_email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** Reset Password Resets the user's password by verifying the phone number and new password. POST /api/v1/user/auth/reset_password */
export async function authResetPassword(
  body: API.PasswordResetRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean>('/api/v1/user/auth/reset_password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** Send verification code Sends a verification code to the provided phone number or email address. POST /api/v1/user/auth/send_verification_code */
export async function authSendVerificationCode(
  body: API.VerificationCodeSendRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean>(
    '/api/v1/user/auth/send_verification_code',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    }
  )
}
