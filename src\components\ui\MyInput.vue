<template>
  <el-input v-model.trim="modelValue" v-bind="$attrs">
    <template #suffix><slot name="suffix"></slot></template>
  </el-input>
</template>
<script lang="ts" setup>
const modelValue = defineModel<string>()
</script>
<style scoped lang="scss">
.el-input {
  :deep(.el-input__wrapper) {
    @apply border-[2px] border-tertiary rounded-xl shadow-none px-4 py-2.5;

    &:hover {
      @apply border-brand;
    }
    .el-input__inner {
      @apply font-semibold text-primary h-6 placeholder:text-placeholder;
    }
  }
}
</style>
