import { ref } from 'vue'
import { pageTaskUnits } from '@/clients/api/zuohua'
import { PAGE_SIZE } from '@/config'

export function useHistoryList() {
  const data = ref<API.TaskUnitVO[]>([])
  const total = ref(0)
  const hasNextPage = ref(false)
  const loading = ref(false)

  const fetchData = async ({
    drawType,
    page,
    startCreateTime,
    endCreateTime,
    pageSize = PAGE_SIZE,
  }: {
    drawType?: number
    page?: number
    startCreateTime?: number
    endCreateTime?: number
    pageSize?: number
  }) => {
    try {
      loading.value = true
      const res = await pageTaskUnits({
        drawType: drawType,
        page: page,
        pageSize,
        startCreateTime,
        endCreateTime,
      })
      data.value = res?.data?.list ?? []
      total.value = res?.data?.total ?? 0
      hasNextPage.value =
        (res?.data?.total ?? 0) >
        (res?.data?.pageNum ?? 0) * (res?.data?.pageSize ?? 0)
    } catch (err) {
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  return {
    data,
    fetchData,
    total,
    hasNextPage,
    loading,
  }
}
