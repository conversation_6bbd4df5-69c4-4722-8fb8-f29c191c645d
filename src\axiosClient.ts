import axios from 'axios'

import type {
  AxiosError,
  InternalAxiosRequestConfig,
  AxiosProgressEvent,
  AxiosRequestConfig,
} from 'axios'

import { getDevice, getAuth } from '@/utils'

type RequestConfig<TData = unknown> = {
  baseURL?: string
  url?: string
  method: 'GET' | 'PUT' | 'PATCH' | 'POST' | 'DELETE'
  params?: unknown
  data?: TData
  responseType?:
  | 'arraybuffer'
  | 'blob'
  | 'document'
  | 'json'
  | 'text'
  | 'stream'
  signal?: AbortSignal
  headers?: AxiosRequestConfig['headers']
  onUploadProgress?: (e: AxiosProgressEvent) => void
}

export type ResponseConfig<TData = unknown> = {
  data?: TData
  code?: number
  message?: string
  type?: string
}

export const instance = axios.create({
  headers: {
    'Content-Type': 'application/json',
  },
})

instance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    if (config.headers) {
      config.headers['X-Device-Id'] = getDevice()
      // 默认127.0.0.1，网关会替换真实ip
      config.headers['X-Client-Ip'] = '*********'
      config.headers['Authorization'] = getAuth()
    }

    return config
  },
  error => Promise.reject(error)

)

instance.interceptors.response.use(
  res => {
    const data = res.data
    if (!data?.success) {
      return Promise.reject(data)
    }
    return data
  },
  error => {
    console.log(
      'resError',
      error.response && error.response.data,
      error.response && error.response.config.url
    )
    return Promise.reject((error.response && error.response.data) || {})
  }
)

const axiosClient = async <TData, TError = unknown, TVariables = unknown>(
  url: string,
  config: RequestConfig<TVariables>
): Promise<TData> => {
  return instance
    .request<ResponseConfig<TData>, TData>({
      baseURL: import.meta.env.VITE_PUBLIC_API,
      url,
      ...config,
    })
    .catch((e: AxiosError<TError>) => {
      throw e
    })
}

export default axiosClient
