import CryptoJS from 'crypto-js'
import <PERSON><PERSON> from 'js-cookie'
import { UAParser } from 'ua-parser-js'

export function encrypt(plaintText: string, cryptojsKey: string) {
  const options = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  }
  const key = CryptoJS.enc.Utf8.parse(cryptojsKey)
  const encryptedData = CryptoJS.AES.encrypt(plaintText, key, options)
  const encryptedBase64Str = encryptedData
    .toString()
    .replace(/\//g, '_')
    .replace(/\+/g, '-')

  return encryptedBase64Str
}

export function decrypt(encryptedBase64Str: string, cryptojsKey: string) {
  const vals = encryptedBase64Str.replace(/\-/g, '+').replace(/_/g, '/')
  const options = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  }
  const key = CryptoJS.enc.Utf8.parse(cryptojsKey)
  const decryptedData = CryptoJS.AES.decrypt(vals, key, options)
  const decryptedStr = CryptoJS.enc.Utf8.stringify(decryptedData)
  return decryptedStr
}

export const CRYPTOJSKEY = 'WTAHAPPYACTIVITY'

export const COOKIE_TOKEN_NAME = 'taoandcompany-token'

export const getAuth = () => Cookie.get(COOKIE_TOKEN_NAME)
export const setAuth = (token: string) => {
  Cookie.set(COOKIE_TOKEN_NAME, token || '', {
    expires: 365,
  })
}
export const removeAuth = () => Cookie.remove(COOKIE_TOKEN_NAME)

export const getDevice = () => {
  const parser = new UAParser()
  const res = parser.getResult()
  return encrypt(
    `${res.device.toString()}-${res.engine.toString()}-${res.browser.toString()}`,
    'TAO&COMPANY'
  )
}
