// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** 此处后端没有提供注释 POST /complete/callback */
export async function completeCallBack(
  body: API.CompleteCallBackParam,
  options?: { [key: string]: any }
) {
  return request<API.ResultString>('/complete/callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /test/create_task */
export async function createTask(
  body: Record<string, any>,
  options?: { [key: string]: any }
) {
  return request<API.ResultString>('/test/create_task', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /wujie/complete/callback */
export async function wujieCompleteCallBack(
  body: API.CompleteCallBackParam,
  options?: { [key: string]: any }
) {
  return request<API.ResultString>('/wujie/complete/callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** 此处后端没有提供注释 POST /wujie/gpu_complete/callback */
export async function wujieGpuCompleteCallBack(
  body: API.CompleteCallBackParam,
  options?: { [key: string]: any }
) {
  return request<API.ResultString>('/wujie/gpu_complete/callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}
