// @ts-ignore
/* eslint-disable */
import request from '../../axiosClient'

/** View User Profile Allows the user to view their profile information, including personal details, etc. GET /api/v1/user/base/profile */
export async function viewUserProfile(options?: { [key: string]: any }) {
  return request<API.ResultUserResponseVO>('/api/v1/user/base/profile', {
    method: 'GET',
    ...(options || {}),
  })
}

/** Update User Profile Allows the user to update their profile information such as name, email, etc. PUT /api/v1/user/base/profile */
export async function updateUserProfile(
  body: API.UserUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean>('/api/v1/user/base/profile', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}
