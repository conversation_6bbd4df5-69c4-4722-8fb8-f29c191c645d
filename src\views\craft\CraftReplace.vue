<template>
  <SubPageLayout :confirm-loading="confirmLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload
          v-model:value="uploadImage"
          :drag="true"
          @onUrlChange="onUrlChangeFun"
        />
        <el-button
          @click="editImg"
          style="border-radius: 0.5rem"
          v-if="uploadImage"
          >编辑重绘区域</el-button
        >
        <FormItem label="画面描述">
          <MyTextarea
            v-model="prompt"
            placeholder="请输入画面描述"
            showCount
            :maxlength="1000"
          />
        </FormItem>
        <FormItem label="工艺选择">
          <MySelect v-model="selectVal" :list="craftList" />
        </FormItem>
        <FormItem label="生成数量">
          <GenerateCount :count="count" @onChangeSelect="selectChange" />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
  <PaintModal
    v-model:visible="paintVisible"
    :bottomImage="uploadImage"
    @confirm="sureMask"
    :maskImage="maskImage || undefined"
  />
</template>
<script setup lang="ts">
import { craftSpecialFill, QiNiuImgUpload } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { toBlob } from '@/utils'
import { useImgwork } from '@/store'
const useImg = useImgwork()
const uploadImage = ref()
const maskImage = ref()
const prompt = ref('')
const confirmLoading = ref(false)
const paintVisible = ref(false)
const activeIndex = ref(0)
const originImg = ref()
const originImgPath = ref()
const artwork = useArtwork()
const userStore = useUserInfoStore()
const route = useRoute()
const router = useRouter()
const maskOne = ref()
const count = ref(2)
const selectVal = ref('司马克')
const craftList = [
  { label: '司马克', value: '司马克' },
  { label: '绣花--镂空绣', value: '绣花--镂空绣' },
  { label: '烫钻(研发中)', value: '烫钻' },
  { label: '压杆(研发中)', value: '压杆' },
  { label: '订珠(研发中)', value: '订珠' },
  { label: '对丝(研发中)', value: '对丝' },
  { label: '打条(研发中)', value: '打条' },
]
const editImg = async () => {
  paintVisible.value = true
  // if (uploadImage.value.includes('api.taologyai.com')) {
  //   try {
  //     let formData = new FormData()
  //     formData.append('image_url', uploadImage.value)
  //     const res = await QiNiuImgUpload(formData)
  //     if (res.data) {
  //       uploadImage.value = res.data
  //     }
  //   } catch (error: any) {
  //     // ElMessage.error(error.message || '出错了')
  //   }
  // }
}
const selectChange = (e: number) => {
  count.value = e
}
const onUrlChangeFun = (e: string) => {
  maskImage.value = null
  maskOne.value = null
  originImgPath.value = e
}
const sureMask = (e: { file: File; url: string }) => {
  maskOne.value = e.file
  maskImage.value = e.url
}
const { pollCreatingArtworks, stopCreatingPoll } = useCreatingPoll()

const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  if (!maskImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请涂抹图片')
    return
  }
  if (!prompt.value) {
    ElMessage.closeAll()
    ElMessage.error('请输入画面描述')
    return
  }
  try {
    confirmLoading.value = true
    const response = await toBlob(uploadImage.value)
    originImg.value = response
    const maskResponse = await toBlob(maskImage.value)
    maskOne.value = maskResponse
    let formData = new FormData()
    formData.append('image_load', originImg.value)
    formData.append('image_url', uploadImage.value)
    formData.append('mask_load', maskOne.value)
    formData.append('image_mask_url', maskImage.value)
    formData.append('prompt', prompt.value)
    formData.append('craft_name', selectVal.value)
    formData.append('n_iter', String(count.value))
    const res = await craftSpecialFill(formData)
    confirmLoading.value = false
    activeIndex.value = 0
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}

const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}
watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.change_head_plusFields?.imageUrl
    maskImage.value = params?.change_head_plusFields?.imageMaskUrl
    prompt.value = params?.change_head_plusFields?.prompt || ''
    count.value = params?.change_head_plusFields?.n_iter
    selectVal.value = params?.change_head_plusFields?.craftName
  }
)
watch(
  () => useImg.imgUrlCraft,
  () => {
    uploadImage.value = useImg.imgUrlCraft
    maskImage.value = null
    artwork.creatingArtworks = []
  }
)
onMounted(async () => {
  uploadImage.value = useImg.imgUrlCraft
})
onBeforeUnmount(() => {
  stopCreatingPoll()
  useImg.setUpdateUrlCraft('')
})
</script>
<style scoped lang="scss">
:deep(.el-button:hover) {
  color: #606022;
  background: none;
  border-color: #dcdfe6;
}
</style>
