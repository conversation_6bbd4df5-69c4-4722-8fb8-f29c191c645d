<template>
  <SubPageLayout :confirm-loading="isLoading" @confirm="handleConfirm">
    <template #form>
      <div class="flex flex-col gap-4 h-full">
        <PictureUpload v-model:value="uploadImage" :drag="true" />
        <!-- <FormItem label="颜色层次">
          <StepInput
            v-model="layerCount"
            :min="1"
            :max="32"
            :disabled="!uploadImage"
          />
        </FormItem> -->
        <FormItem label="高精度模式">
          <GenerateCount
            :counts="radioList"
            :count="radioCount"
            @onChangeSelect="selectChange1"
          />
        </FormItem>
      </div>
    </template>

    <div class="h-full flex items-center justify-center">
      <DefaultContentArea
        class="h-full"
        v-if="!artwork.creatingArtworks.length"
      />
      <div class="w-[624px] h-[624px]" v-else>
        <ImageViewer
          :previewImages="artwork.creatingArtworks"
          :activeIndex="activeIndex"
          @slideChange="onSlideChange"
        />
      </div>
    </div>
  </SubPageLayout>
</template>
  <script setup lang="ts">
import { processImageToPsd } from '@/clients/api/zuohua'
import { useCreatingPoll } from '@/hooks/useCreatingPoll'
import { useArtwork, useUserInfoStore } from '@/store'
import { onBeforeUnmount, ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { toBlob } from '@/utils'
const uploadImage = ref<string | null | undefined>()
const confirmLoading = ref(false)
const activeIndex = ref(0)
const artwork = useArtwork()
const userStore = useUserInfoStore()
const originImg = ref()
const layerCount = ref()
const radioList = ref(['否', '是'])
const radioCount = ref<string | boolean>('否')
const selectChange1 = (e: string) => {
  radioCount.value = e
}
const {
  pollCreatingArtworks,
  stopCreatingPoll,
  loading: pollingLoading,
} = useCreatingPoll()

// 合并API调用loading和轮询loading状态
const isLoading = computed(() => confirmLoading.value || pollingLoading.value)

const handleConfirm = async () => {
  if (!uploadImage.value) {
    ElMessage.closeAll()
    ElMessage.error('请上传图片')
    return
  }
  // if (!layerCount.value) {
  //   ElMessage.closeAll()
  //   ElMessage.error('请输入颜色层次')
  //   return
  // }
  try {
    confirmLoading.value = true
    const response = await toBlob(uploadImage.value)
    originImg.value = response
    let flag
    flag = radioCount.value == '否' ? true : false
    let formData = new FormData()
    formData.append('image', originImg.value)
    formData.append('image_url', uploadImage.value)
    formData.append('enable_small_segment_merge', flag)
    const res = await processImageToPsd(formData)
    confirmLoading.value = false
    userStore.updateDrawingTimes()
    pollCreatingArtworks({
      ids: res?.data?.ids ?? [],
    })
  } catch (error: any) {
    confirmLoading.value = false
    if (error == 'Unauthorized') {
      ElMessage.error('请登录后使用')
    } else {
      ElMessage.error(error.message || '出错了')
    }
  }
}
const onSlideChange = (index: number) => {
  console.log(index, 'onSlideChange')
  activeIndex.value = index
}

watch(
  () => artwork.drawParams,
  params => {
    if (!params) return
    artwork.setCreatingArtworks([params])
    uploadImage.value = params?.layerSeparationFields?.inImgUrl
    layerCount.value = params?.layerSeparationFields?.numColors
  }
)

onBeforeUnmount(() => {
  stopCreatingPoll()
})
</script>
<style scoped lang="scss">
</style>
      