<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import { computed, ref } from 'vue'
import { ElIcon } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import flag from '@/assets/flag.svg'
import flag1 from '@/assets/flag1.svg'
import flag2 from '@/assets/flag2.svg'
import { menus } from './const'
import MyIcon from '@/components/ui/MyIcon.vue'
import HeaderUserCenter from './HeaderUserCenter.vue'

const { headerType } = defineProps<{ headerType?: string }>()
const router = useRouter()
const route = useRoute()

const activeIndex = computed(() => {
  return `/${route.path.split('/')[1] || ''}`
})
const jump = () => {
  window.location.href = 'http://taologyai.com/en'
}
const handleSelect = (key: string) => {
  router.push(key)
}
</script>

<template>
  <div class="headerWrap">
    <div class="headerContent">
      <div class="flex items-center">
        <div @click="router.push('/')" class="mr-3 pl-6">
          <MyImage
            class="w-[144px]"
            :lazy="false"
            src="/images/logo.png"
            alt="wujieai"
          />
        </div>
        <el-menu
          class="headerMenu"
          mode="horizontal"
          :ellipsis="false"
          @select="handleSelect"
          background-color="transparent"
          text-color="var(--neutral-900)"
          :default-active="activeIndex"
        >
          <el-menu-item v-for="menu in menus" :index="menu.pathname">
            <ElIcon>
              <MyIcon :icon="menu.icon!" :size="24" />
            </ElIcon>
            {{ menu.label }}
          </el-menu-item>
        </el-menu>
      </div>
      <ul class="flex items-center">
        <li class="flex items-center ml-4 cursor-pointer">
          <div class="flex items-center">
            <img :src="flag" alt="" class="mr-1" />
            <div class="w-[16px] flex justify-center">
              <el-dropdown>
                <el-icon>
                  <arrow-down />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="jump">
                      <img :src="flag1" alt="" class="mr-1"
                    /></el-dropdown-item>
                    <el-dropdown-item>
                      <img :src="flag2" alt="" class="mr-1"
                    /></el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </li>
        <li
          class="w-[56px] ml-6 cursor-pointer flex justify-center items-center"
          @click="$emit('onOpen')"
        >
          <HeaderUserCenter />
        </li>
      </ul>
    </div>
    <div :class="['headerBgLeft', headerType && 'hasHeaderBgLeft']"></div>
    <div class="headerBgRight"></div>
  </div>
  <div class="h-[80px]" />
</template>

<style scoped src="./Header.scss"></style>
